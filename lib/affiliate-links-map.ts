import { AffiliateLink } from "./api-client"

// Define the structure of an affiliate link with tags
export interface TaggedAffiliateLink extends AffiliateLink {
  tags: string[]
  provider: string
}

// Define all possible tags that can be used for affiliate links
// These tags will be used in the OpenAI prompt to ensure consistent tagging
export const AFFILIATE_LINK_TAGS = [
  // Accommodation tags
  "accommodation",
  "hotel",
  "lodging",
  "stay",
  "hostel",
  "apartment",
  "rental",

  // Transportation tags
  "flight",
  "air travel",
  "plane",
  "airline",
  "transportation",
  "car rental",
  "transfer",
  "taxi",
  "train",
  "bus",
  "ferry",

  // Activities tags
  "tour",
  "activity",
  "experience",
  "sightseeing",
  "adventure",
  "excursion",

  // Insurance tags
  "insurance",
  "coverage",
  "protection",
  "safety",

  // Booking tags
  "booking",
  "reservation",
  "ticket",

  // Local experience tags
  "local",
  "guide",
  "cultural",
  "food",
  "dining",
  "restaurant",

  // Equipment tags
  "equipment",
  "gear",
  "clothing",
  "luggage",
  "backpack",
  "camera",

  // Planning tags
  "planning",
  "itinerary",
  "schedule",
  "map",
  "guide book",
]

// Create a map of affiliate links with appropriate tags
export const affiliateLinksMap: TaggedAffiliateLink[] = [
  {
    url: "https://www.booking.com",
    title: "Book Accommodation",
    description: "Find and book hotels, apartments, and other accommodations",
    tags: [
      "accommodation",
      "hotel",
      "lodging",
      "stay",
      "booking",
      "reservation",
      "hostel",
      "apartment",
      "rental",
    ],
    provider: "Booking.com",
  },
  {
    url: "https://www.skyscanner.com",
    title: "Book Flights",
    description: "Compare and book flights from hundreds of airlines",
    tags: ["flight", "air travel", "plane", "airline", "booking", "transportation", "ticket"],
    provider: "Skyscanner",
  },
  {
    url: "https://www.rentalcars.com",
    title: "Book Transportation",
    description: "Rent cars, find airport transfers, and book other ground transportation",
    tags: ["transportation", "car rental", "transfer", "taxi", "booking", "reservation"],
    provider: "RentalCars",
  },
  {
    url: "https://www.getyourguide.com",
    title: "Book Tours & Activities",
    description: "Discover and book tours, activities, and experiences",
    tags: ["tour", "activity", "experience", "sightseeing", "booking", "adventure", "excursion"],
    provider: "GetYourGuide",
  },
  {
    url: "https://www.worldnomads.com",
    title: "Get Travel Insurance",
    description: "Protect your trip with comprehensive travel insurance",
    tags: ["insurance", "coverage", "protection", "safety"],
    provider: "World Nomads",
  },
  {
    url: "https://www.viator.com",
    title: "Book Local Experiences",
    description: "Find unique local experiences and activities",
    tags: ["local", "experience", "activity", "tour", "booking", "guide", "cultural"],
    provider: "Viator",
  },
  {
    url: "https://www.tripadvisor.com",
    title: "Find Restaurants & Attractions",
    description: "Discover top-rated restaurants and attractions",
    tags: ["food", "dining", "restaurant", "activity", "attraction", "local"],
    provider: "TripAdvisor",
  },
  {
    url: "https://www.rei.com",
    title: "Get Travel Gear",
    description: "Shop for travel equipment and outdoor gear",
    tags: ["equipment", "gear", "clothing", "luggage", "backpack", "camera"],
    provider: "REI",
  },
  {
    url: "https://www.lonelyplanet.com",
    title: "Get Travel Guides",
    description: "Access comprehensive travel guides and planning resources",
    tags: ["planning", "itinerary", "guide book", "map"],
    provider: "Lonely Planet",
  },
]

// Function to find the best matching affiliate link for a task based on its tags
export function findAffiliateLink(
  taskTitle: string,
  taskDescription: string,
  destination?: string
): TaggedAffiliateLink | null {
  // Convert task title and description to lowercase for case-insensitive matching
  const taskText = `${taskTitle} ${taskDescription} ${destination || ""}`.toLowerCase()

  // Find the affiliate link with the most matching tags
  let bestMatch: TaggedAffiliateLink | null = null
  let maxMatchCount = 0

  for (const link of affiliateLinksMap) {
    let matchCount = 0

    // Count how many tags from the affiliate link match the task text
    for (const tag of link.tags) {
      if (taskText.includes(tag.toLowerCase())) {
        matchCount++
      }
    }

    // Update the best match if this link has more matching tags
    if (matchCount > maxMatchCount) {
      maxMatchCount = matchCount
      bestMatch = link
    }
  }

  // Return the best matching affiliate link, or null if no good match was found
  return maxMatchCount > 0 ? bestMatch : null
}

// Function to find multiple affiliate links for a task (max 2)
export function findMultipleAffiliateLinks(
  taskTitle: string,
  taskDescription: string,
  maxLinks: number = 2,
  destination?: string
): TaggedAffiliateLink[] {
  // Convert task title and description to lowercase for case-insensitive matching
  const taskText = `${taskTitle} ${taskDescription} ${destination || ""}`.toLowerCase()

  // Create an array to store matches with their scores
  const matches: Array<{ link: TaggedAffiliateLink; score: number }> = []

  // Calculate match score for each affiliate link
  for (const link of affiliateLinksMap) {
    let matchCount = 0

    // Count how many tags from the affiliate link match the task text
    for (const tag of link.tags) {
      if (taskText.includes(tag.toLowerCase())) {
        matchCount++
      }
    }

    // If there's at least one match, add to our matches array
    if (matchCount > 0) {
      matches.push({ link, score: matchCount })
    }
  }

  // Sort matches by score (highest first) and take the top maxLinks
  return matches
    .sort((a, b) => b.score - a.score)
    .slice(0, maxLinks)
    .map((match) => match.link)
}

// Define the static task suggestions with their corresponding affiliate link tags
export const staticTaskSuggestions = [
  {
    title: "Book a Flight",
    description: "Search and book flights to your destination",
    tags: ["flight", "air travel", "plane", "airline", "booking"],
    category: "booking" as const,
    priority: "high" as const,
  },
  {
    title: "Book Accommodation",
    description: "Find and book hotels or other accommodations",
    tags: ["accommodation", "hotel", "lodging", "stay", "booking"],
    category: "booking" as const,
    priority: "high" as const,
  },
  {
    title: "Book Transportation",
    description: "Arrange for local transportation at your destination",
    tags: ["transportation", "car rental", "transfer", "taxi", "booking"],
    category: "booking" as const,
    priority: "medium" as const,
  },
  {
    title: "Get Travel Insurance",
    description: "Protect your trip with comprehensive travel insurance",
    tags: ["insurance", "coverage", "protection", "safety"],
    category: "preparation" as const,
    priority: "medium" as const,
  },
  {
    title: "Plan Your Itinerary",
    description: "Create a day-by-day plan for your trip",
    tags: ["planning", "itinerary", "schedule"],
    category: "planning" as const,
    priority: "high" as const,
  },
]

// Function to check if a task is similar to any of the static suggestions
export function isTaskSimilarToStaticSuggestion(
  taskTitle: string,
  staticSuggestions = staticTaskSuggestions
): boolean {
  const normalizedTaskTitle = taskTitle.toLowerCase()

  // Check if the task title contains keywords from any static suggestion
  return staticSuggestions.some((suggestion) => {
    const suggestionKeywords = [...suggestion.tags, suggestion.title.toLowerCase()]
    return suggestionKeywords.some((keyword) => normalizedTaskTitle.includes(keyword))
  })
}

// Function to filter static suggestions based on existing tasks
export function filterStaticSuggestions(
  tasks: { title: string; description?: string }[],
  suggestions = staticTaskSuggestions
): typeof staticTaskSuggestions {
  // If there are no tasks, return all static suggestions
  if (!tasks.length) return suggestions

  // Filter out suggestions that are similar to existing tasks
  return suggestions.filter((suggestion) => {
    return !tasks.some((task) => {
      const taskTitle = task.title.toLowerCase()
      const taskDesc = (task.description || "").toLowerCase()
      const suggestionTitle = suggestion.title.toLowerCase()

      // Check if the task title contains the suggestion title or vice versa
      if (taskTitle.includes(suggestionTitle) || suggestionTitle.includes(taskTitle)) {
        return true
      }

      // Check if the task title contains any of the suggestion tags
      if (suggestion.tags.some((tag) => taskTitle.includes(tag.toLowerCase()))) {
        return true
      }

      // Check for similar keywords or phrases in title and description
      if (taskDesc) {
        // Check if description contains suggestion title
        if (taskDesc.includes(suggestionTitle)) {
          return true
        }

        // Check if description contains any of the suggestion tags
        if (suggestion.tags.some((tag) => taskDesc.includes(tag.toLowerCase()))) {
          return true
        }

        // Check for significant word matches
        const taskWords = [...taskTitle.split(" "), ...taskDesc.split(" ")]
        const suggestionWords = [
          ...suggestionTitle.split(" "),
          ...suggestion.tags.join(" ").split(" "),
        ]

        // Count matching significant words (longer than 3 characters)
        const significantMatches = taskWords.filter(
          (word) => word.length > 3 && suggestionWords.some((sWord) => sWord === word)
        ).length

        // If more than 2 significant words match, consider it similar
        if (significantMatches > 2) {
          return true
        }
      }

      return false
    })
  })
}
