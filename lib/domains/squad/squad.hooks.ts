"use client"

import { useEffect, useState } from "react"
import { useSquadStore } from "./squad.store"
import { Squad, SquadCreateData, SquadUpdateData } from "./squad.types"
import { SquadService } from "./squad.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { ServiceResponse } from "../base/base.types"
import { useRealtimeSquad, useRealtimeUserSquads } from "./squad.realtime.hooks"

// Export real-time hooks
export * from "./squad.realtime.hooks"

/**
 * Hook to get all squads for the current user
 */
export const useUserSquads = (useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    return useRealtimeUserSquads()
  }

  // Otherwise use the regular store
  const user = useUser()
  const { squads, loading, error, fetchUserSquads } = useSquadStore()

  useEffect(() => {
    if (user?.uid) {
      fetchUserSquads(user.uid)
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, fetchUserSquads])

  return { squads, loading, error }
}

/**
 * Hook to get a specific squad
 */
export const useSquad = (squadId: string, useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    return useRealtimeSquad(squadId)
  }

  // Otherwise use the regular store
  const { currentSquad, loading, error, fetchSquad } = useSquadStore()

  useEffect(() => {
    if (squadId) {
      fetchSquad(squadId)
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [squadId, fetchSquad])

  return { squad: currentSquad, loading, error }
}

/**
 * Hook to create a squad
 */
export const useCreateSquad = () => {
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createSquad } = useSquadStore()

  const create = async (squadData: SquadCreateData) => {
    try {
      setCreating(true)
      setError(null)
      const squadId = await createSquad(squadData)
      setCreating(false)
      return squadId
    } catch (err) {
      setError(err as Error)
      setCreating(false)
      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to update a squad with optimistic updates
 */
export const useUpdateSquad = (squadId?: string) => {
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateSquad, currentSquad, setCurrentSquad } = useSquadStore()

  const update = async (data: SquadUpdateData, id?: string) => {
    const targetId = id || squadId
    if (!targetId) {
      setError(new Error("No squad ID provided"))
      return false
    }

    try {
      setUpdating(true)
      setError(null)

      // Optimistic update if we have the current squad
      let previousData: Partial<Squad> | null = null
      if (currentSquad && currentSquad.id === targetId) {
        previousData = { ...currentSquad }

        // Apply optimistic update to UI
        setCurrentSquad({
          ...currentSquad,
          ...data,
        })
      }

      // Perform the actual update
      const success = await updateSquad(targetId, data)

      // If failed and we have previous data, revert the optimistic update
      if (!success && previousData) {
        setCurrentSquad(previousData as Squad)
      }

      setUpdating(false)
      return success
    } catch (err) {
      // Revert optimistic update on error
      if (currentSquad && currentSquad.id === targetId) {
        setCurrentSquad(currentSquad)
      }

      setError(err as Error)
      setUpdating(false)
      return false
    }
  }

  return { update, updating, error }
}

/**
 * Hook to delete a squad
 */
export const useDeleteSquad = () => {
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const deleteSquad = async (squadId: string): Promise<ServiceResponse> => {
    if (!squadId) {
      const error = new Error("No squad ID provided")
      setError(error)
      return { success: false, error }
    }

    try {
      setDeleting(true)
      setError(null)

      // Get the squad to check if it exists
      const squad = await SquadService.getSquad(squadId)
      if (!squad) {
        const error = new Error("Squad not found")
        setError(error)
        setDeleting(false)
        return { success: false, error }
      }

      // Delete the squad using BaseService.delete
      const result = await SquadService.deleteSquad(squadId)

      setDeleting(false)
      return result
    } catch (err) {
      const error = err as Error
      setError(error)
      setDeleting(false)
      return { success: false, error }
    }
  }

  return { deleteSquad, deleting, error }
}

/**
 * Hook to leave a squad
 */
export const useLeaveSquad = () => {
  const [leaving, setLeaving] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { removeMember } = useSquadStore()
  const user = useUser()

  const leaveSquad = async (squadId: string, userId?: string): Promise<ServiceResponse> => {
    const targetUserId = userId || user?.uid

    if (!squadId) {
      const error = new Error("No squad ID provided")
      setError(error)
      return { success: false, error }
    }

    if (!targetUserId) {
      const error = new Error("No user ID provided")
      setError(error)
      return { success: false, error }
    }

    try {
      setLeaving(true)
      setError(null)

      // Get the squad to check if it exists and validate leave conditions
      const squad = await SquadService.getSquad(squadId)

      if (!squad) {
        const error = new Error("Squad not found")
        setError(error)
        setLeaving(false)
        return { success: false, error }
      }

      // Check if user is a member
      if (!squad.members.includes(targetUserId)) {
        const error = new Error("User is not a member of this squad")
        setError(error)
        setLeaving(false)
        return { success: false, error }
      }

      // Check if user is the leader and the only member
      if (squad.leaderId === targetUserId && squad.members.length === 1) {
        const error = new Error(
          "Squad leader cannot leave as the only member. Delete the squad instead."
        )
        setError(error)
        setLeaving(false)
        return { success: false, error }
      }

      // If user is the leader but not the only member, we need to transfer leadership
      let result: ServiceResponse

      if (squad.leaderId === targetUserId && squad.members.length > 1) {
        // Find another member to transfer leadership to
        const newLeaderId = squad.members.find((id) => id !== targetUserId)

        if (!newLeaderId) {
          const error = new Error("Failed to find a new leader for the squad")
          setError(error)
          setLeaving(false)
          return { success: false, error }
        }

        // Update the squad with the new leader
        const updateResult = await SquadService.updateSquad(squadId, { leaderId: newLeaderId })

        if (!updateResult.success) {
          setError(updateResult.error as Error)
          setLeaving(false)
          return updateResult
        }
      }

      // Remove the member
      result = await SquadService.removeMember(squadId, targetUserId)

      setLeaving(false)
      return result
    } catch (err) {
      const error = err as Error
      setError(error)
      setLeaving(false)
      return { success: false, error }
    }
  }

  return { leaveSquad, leaving, error }
}

/**
 * Hook to manage squad members
 */
export const useSquadMembers = (squadId?: string) => {
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { addMember, removeMember } = useSquadStore()

  const add = async (userId: string, id?: string) => {
    const targetId = id || squadId
    if (!targetId) {
      setError(new Error("No squad ID provided"))
      return false
    }

    try {
      setProcessing(true)
      setError(null)
      const success = await addMember(targetId, userId)
      setProcessing(false)
      return success
    } catch (err) {
      setError(err as Error)
      setProcessing(false)
      return false
    }
  }

  const remove = async (userId: string, id?: string) => {
    const targetId = id || squadId
    if (!targetId) {
      setError(new Error("No squad ID provided"))
      return false
    }

    try {
      setProcessing(true)
      setError(null)
      const success = await removeMember(targetId, userId)
      setProcessing(false)
      return success
    } catch (err) {
      setError(err as Error)
      setProcessing(false)
      return false
    }
  }

  return { add, remove, processing, error }
}

/**
 * Hook to check if a user is the squad leader
 */
export const useIsSquadLeader = (squadId: string) => {
  const user = useUser()
  const [isLeader, setIsLeader] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkLeaderStatus = async () => {
      if (user?.uid && squadId) {
        setLoading(true)
        try {
          const result = await SquadService.isUserSquadLeader(user.uid, squadId)
          setIsLeader(result)
        } catch (error) {
          console.error("Error checking squad leader status:", error)
          setIsLeader(false)
        } finally {
          setLoading(false)
        }
      } else {
        setIsLeader(false)
        setLoading(false)
      }
    }

    checkLeaderStatus()
  }, [user, squadId])

  return { isLeader, loading }
}

/**
 * Hook to check if a user is a member of a squad
 */
export const useIsSquadMember = (squadId: string) => {
  const user = useUser()
  const [isMember, setIsMember] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkMemberStatus = async () => {
      if (user?.uid && squadId) {
        setLoading(true)
        try {
          const result = await SquadService.isUserSquadMember(user.uid, squadId)
          setIsMember(result)
        } catch (error) {
          console.error("Error checking squad member status:", error)
          setIsMember(false)
        } finally {
          setLoading(false)
        }
      } else {
        setIsMember(false)
        setLoading(false)
      }
    }

    checkMemberStatus()
  }, [user, squadId])

  return { isMember, loading }
}

/**
 * Hook to get the squad leader for a specific squad
 * @param squadId The ID of the squad
 * @returns Object containing the squad leader user object and loading state
 */
export const useSquadLeader = (squadId: string) => {
  const { getSquadLeader } = useSquadStore()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Get the squad leader using the store selector
  const squadLeader = getSquadLeader(squadId)

  // Set loading to false once we have the result
  useEffect(() => {
    setLoading(false)
  }, [squadLeader])

  return { squadLeader, loading, error }
}
