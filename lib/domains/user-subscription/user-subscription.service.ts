import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionCreateData,
  UserSubscriptionUpdateData,
} from "./user-subscription.types"

/**
 * User subscription service for Firebase operations
 */
export class UserSubscriptionService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Get a user's subscription
   * @param userId User ID
   * @returns The user subscription or null if not found
   */
  static async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const subscriptionDoc = await getDoc(doc(db, this.COLLECTION, userId))

      if (subscriptionDoc.exists()) {
        return { id: subscriptionDoc.id, ...subscriptionDoc.data() } as UserSubscription
      }

      // If no subscription document exists, create a default free subscription
      const defaultSubscription: UserSubscriptionCreateData = {
        userId,
        stripeCustomerId: "",
        subscriptionId: "",
        subscriptionStatus: null,
        subscriptionPlan: "free",
        subscriptionCurrentPeriodEnd: null,
      }

      await this.createUserSubscription(userId, defaultSubscription)

      return {
        id: userId,
        ...defaultSubscription,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      } as UserSubscription
    } catch (error) {
      console.error("Error getting user subscription:", error)
      return null
    }
  }

  /**
   * Create a user subscription
   * @param userId User ID
   * @param subscriptionData Subscription data
   * @returns Service response
   */
  static async createUserSubscription(
    userId: string,
    subscriptionData: UserSubscriptionCreateData
  ): Promise<ServiceResponse> {
    try {
      await setDoc(doc(db, this.COLLECTION, userId), {
        ...subscriptionData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error creating user subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a user's subscription
   * @param userId User ID
   * @param subscriptionData Subscription data to update
   * @returns Service response
   */
  static async updateUserSubscription(
    userId: string,
    subscriptionData: UserSubscriptionUpdateData
  ): Promise<ServiceResponse> {
    try {
      const subscriptionRef = doc(db, this.COLLECTION, userId)
      const subscriptionDoc = await getDoc(subscriptionRef)

      if (subscriptionDoc.exists()) {
        // Update existing subscription
        await updateDoc(subscriptionRef, {
          ...subscriptionData,
          updatedAt: serverTimestamp(),
        })
      } else {
        // Create new subscription if it doesn't exist
        await this.createUserSubscription(userId, {
          userId,
          stripeCustomerId: subscriptionData.stripeCustomerId || "",
          subscriptionId: subscriptionData.subscriptionId || "",
          subscriptionStatus: subscriptionData.subscriptionStatus || null,
          subscriptionPlan: subscriptionData.subscriptionPlan || "free",
          subscriptionCurrentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || null,
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating user subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user has an active subscription
   * @param userId User ID
   * @returns True if the user has an active subscription
   */
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(userId)

      if (!subscription) return false

      return (
        subscription.subscriptionStatus === "active" &&
        (subscription.subscriptionPlan === "monthly" ||
          subscription.subscriptionPlan === "yearly") &&
        (subscription.subscriptionCurrentPeriodEnd
          ? (() => {
              try {
                // Handle Firestore Timestamp objects
                if (
                  subscription.subscriptionCurrentPeriodEnd &&
                  typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function"
                ) {
                  return (
                    new Date((subscription.subscriptionCurrentPeriodEnd as any).toDate()) >
                    new Date()
                  )
                }
                // Handle numeric timestamps (seconds)
                else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
                  const timestamp = subscription.subscriptionCurrentPeriodEnd
                  // If timestamp is in seconds (before year 2033), convert to milliseconds
                  const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
                  return new Date(milliseconds) > new Date()
                }
                // Handle any other unexpected format
                else {
                  console.error(
                    "Unexpected subscriptionCurrentPeriodEnd format:",
                    subscription.subscriptionCurrentPeriodEnd
                  )
                  return true // Default to true to avoid blocking users
                }
              } catch (error) {
                console.error("Error checking subscription expiration:", error)
                return true // Default to true to avoid blocking users
              }
            })()
          : true)
      )
    } catch (error) {
      console.error("Error checking if user has active subscription:", error)
      return false
    }
  }

  /**
   * Check if a user is subscribed (with error handling)
   * This is a safer version of hasActiveSubscription that handles errors gracefully
   * and is designed to be used in UI components
   * @param userId User ID
   * @returns True if the user is subscribed
   */
  static async isUserSubscribed(userId: string): Promise<boolean> {
    try {
      // If no userId is provided, return false
      if (!userId) return false

      const subscription = await this.getUserSubscription(userId)
      if (!subscription) return false

      // Check if subscription is active
      const isActive =
        subscription.subscriptionStatus === "active" &&
        (subscription.subscriptionPlan === "monthly" || subscription.subscriptionPlan === "yearly")

      if (!isActive) return false

      // Check if subscription is expired based on end date
      try {
        // Handle Firestore Timestamp objects
        if (typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function") {
          return new Date((subscription.subscriptionCurrentPeriodEnd as any).toDate()) > new Date()
        }
        // Handle numeric timestamps (seconds)
        else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
          const timestamp = subscription.subscriptionCurrentPeriodEnd
          // If timestamp is in seconds (before year 2033), convert to milliseconds
          const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
          return new Date(milliseconds) > new Date()
        }
        // Handle any other format (like string dates)
        else if (subscription.subscriptionCurrentPeriodEnd) {
          try {
            // Try to parse as a date string
            return new Date(subscription.subscriptionCurrentPeriodEnd as any) > new Date()
          } catch (e) {
            console.error("Failed to parse subscription end date:", e)
            return true // Default to true to avoid blocking users
          }
        }

        return true // Default to true if we can't determine
      } catch (error) {
        console.error("Error checking subscription expiration:", error)
        return true // Default to true to avoid blocking users
      }
    } catch (error) {
      console.error("Error checking if user is subscribed:", error)
      return false // Default to false on error
    }
  }

  /**
   * Get all user subscriptions
   * @returns Array of user subscriptions
   */
  static async getAllUserSubscriptions(): Promise<UserSubscription[]> {
    try {
      const querySnapshot = await getDocs(collection(db, this.COLLECTION))
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserSubscription[]
    } catch (error) {
      console.error("Error getting all user subscriptions:", error)
      return []
    }
  }
}
