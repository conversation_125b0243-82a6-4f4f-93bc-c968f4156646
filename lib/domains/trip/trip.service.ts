import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { Trip, TripCreateData, TripUpdateData } from "./trip.types"
import { UserTripService } from "../user-trip/user-trip.service"

/**
 * Trip service for Firebase operations
 */
export class TripService {
  private static readonly COLLECTION = "trips"

  /**
   * Create a new trip
   * @param tripData Trip data
   * @returns The new trip ID
   */
  static async createTrip(tripData: TripCreateData): Promise<string> {
    try {
      const tripRef = doc(collection(db, this.COLLECTION))
      const tripId = tripRef.id

      await setDoc(tripRef, {
        ...tripData,
        id: tripId,
        tasksCompleted: 0,
        totalTasks: 0,
        createdAt: serverTimestamp(),
      })

      return tripId
    } catch (error) {
      console.error("Error creating trip:", error)
      throw error
    }
  }

  /**
   * Get a trip by ID
   * @param tripId Trip ID
   * @returns The trip data or null if not found
   */
  static async getTrip(tripId: string): Promise<Trip | null> {
    try {
      const tripDoc = await getDoc(doc(db, this.COLLECTION, tripId))

      if (tripDoc.exists()) {
        const data = tripDoc.data()

        // Get the actual attendees from userTrips collection
        const attendees = await UserTripService.getTripAttendees(tripId)

        return { ...data, id: tripId, attendees } as Trip
      }

      return null
    } catch (error) {
      console.error("Error getting trip:", error)
      throw error
    }
  }

  /**
   * Get trips for a user
   * @param userId User ID
   * @returns Array of trips
   */
  static async getUserTrips(userId: string): Promise<Trip[]> {
    try {
      // Query trips where the user is in the attendees array
      const q = query(collection(db, this.COLLECTION), where("attendees", "array-contains", userId))
      const querySnapshot = await getDocs(q)

      // Process each trip and ensure the attendees field is up-to-date
      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const data = doc.data()
          const tripId = doc.id

          // Get the actual attendees from userTrips collection
          const attendees = await UserTripService.getTripAttendees(tripId)

          // Ensure tasksCompleted and totalTasks have default values
          const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
          const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

          return {
            ...data,
            id: tripId,
            attendees,
            tasksCompleted,
            totalTasks,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting user trips:", error)
      throw error
    }
  }

  /**
   * Get trips for a squad
   * @param squadId Squad ID
   * @returns Array of trips
   */
  static async getSquadTrips(squadId: string): Promise<Trip[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("squadId", "==", squadId))
      const querySnapshot = await getDocs(q)

      // Process each trip and ensure the attendees field is up-to-date
      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const data = doc.data()
          const tripId = doc.id

          // Get the actual attendees from userTrips collection
          const attendees = await UserTripService.getTripAttendees(tripId)

          // Ensure tasksCompleted and totalTasks have default values
          const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
          const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

          return {
            ...data,
            id: tripId,
            attendees,
            tasksCompleted,
            totalTasks,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting squad trips:", error)
      throw error
    }
  }

  /**
   * Update a trip
   * @param tripId Trip ID
   * @param tripData Trip data to update
   * @returns Service response
   */
  static async updateTrip(tripId: string, tripData: TripUpdateData): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, tripId), {
        ...tripData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating trip:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user is the trip leader
   * @param userId User ID
   * @param tripId Trip ID
   * @returns True if the user is the trip leader
   */
  static async isUserTripLeader(userId: string, tripId: string): Promise<boolean> {
    try {
      const trip = await this.getTrip(tripId)
      return trip?.leaderId === userId
    } catch (error) {
      console.error("Error checking if user is trip leader:", error)
      return false
    }
  }
}
