"use client"

import { useEffect, useState } from "react"
import { useTripStore } from "./trip.store"
import { TripCreateData, TripUpdateData } from "./trip.types"
import { TripService } from "./trip.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"

// Export real-time hooks
export * from "./trip.realtime.hooks"

/**
 * Hook to get all trips for the current user
 */
export const useUserTrips = (useRealtime: boolean = false) => {
  const user = useUser()
  const { trips, loading, error, fetchUserTrips, subscribeToUserTrips, unsubscribeFromUserTrips } =
    useTripStore()

  useEffect(() => {
    if (user?.uid) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToUserTrips(user.uid)

        // Cleanup on unmount
        return () => {
          unsubscribeFromUserTrips()
        }
      } else {
        // Use regular fetch
        fetchUserTrips(user.uid)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, fetchUserTrips, subscribeToUserTrips, unsubscribeFromUserTrips, useRealtime])

  return { trips, loading, error }
}

/**
 * Hook to get all trips for a squad
 */
export const useSquadTrips = (squadId: string, useRealtime: boolean = false) => {
  const {
    trips,
    loading,
    error,
    fetchSquadTrips,
    subscribeToSquadTrips,
    unsubscribeFromSquadTrips,
  } = useTripStore()

  useEffect(() => {
    if (squadId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToSquadTrips(squadId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromSquadTrips()
        }
      } else {
        // Use regular fetch
        fetchSquadTrips(squadId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [squadId, fetchSquadTrips, subscribeToSquadTrips, unsubscribeFromSquadTrips, useRealtime])

  return { trips, loading, error }
}

/**
 * Hook to get a specific trip
 */
export const useTrip = (tripId: string, useRealtime: boolean = false) => {
  const { currentTrip, loading, error, fetchTrip, subscribeToTrip, unsubscribeFromTrip } =
    useTripStore()

  useEffect(() => {
    if (tripId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToTrip(tripId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromTrip()
        }
      } else {
        // Use regular fetch
        fetchTrip(tripId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [tripId, fetchTrip, subscribeToTrip, unsubscribeFromTrip, useRealtime])

  return { trip: currentTrip, loading, error }
}

/**
 * Hook to create a trip
 */
export const useCreateTrip = () => {
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createTrip } = useTripStore()

  const create = async (tripData: TripCreateData) => {
    try {
      setCreating(true)
      setError(null)
      const tripId = await createTrip(tripData)
      setCreating(false)
      return tripId
    } catch (err) {
      setError(err as Error)
      setCreating(false)
      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to update a trip
 */
export const useUpdateTrip = (tripId?: string) => {
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateTrip } = useTripStore()

  const update = async (data: TripUpdateData, id?: string) => {
    const targetId = id || tripId
    if (!targetId) {
      setError(new Error("No trip ID provided"))
      return false
    }

    try {
      setUpdating(true)
      setError(null)
      const success = await updateTrip(targetId, data)
      setUpdating(false)
      return success
    } catch (err) {
      setError(err as Error)
      setUpdating(false)
      return false
    }
  }

  return { update, updating, error }
}

/**
 * Hook to check if a user is the trip leader
 */
export const useIsTripLeader = (tripId: string) => {
  const user = useUser()
  const [isLeader, setIsLeader] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkLeaderStatus = async () => {
      if (user?.uid && tripId) {
        setLoading(true)
        try {
          const result = await TripService.isUserTripLeader(user.uid, tripId)
          setIsLeader(result)
        } catch (error) {
          console.error("Error checking trip leader status:", error)
          setIsLeader(false)
        } finally {
          setLoading(false)
        }
      } else {
        setIsLeader(false)
        setLoading(false)
      }
    }

    checkLeaderStatus()
  }, [user, tripId])

  return { isLeader, loading }
}
