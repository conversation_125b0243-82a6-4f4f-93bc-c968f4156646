"use client"

import { create } from "zustand"
import { Trip } from "./trip.types"
import { TripService } from "./trip.service"
import { TripRealtimeService } from "./trip.realtime.service"

/**
 * Trip store state interface
 */
interface TripState {
  // State
  trips: Trip[]
  currentTrip: Trip | null
  loading: boolean
  error: Error | null

  // Real-time subscriptions
  tripSubscription: (() => void) | null
  userTripsSubscription: (() => void) | null
  squadTripsSubscription: (() => void) | null

  // Actions
  fetchUserTrips: (userId: string) => Promise<void>
  fetchSquadTrips: (squadId: string) => Promise<void>
  fetchTrip: (tripId: string) => Promise<void>
  createTrip: (tripData: any) => Promise<string | null>
  updateTrip: (tripId: string, tripData: Partial<Trip>) => Promise<boolean>
  clearTrips: () => void
  setCurrentTrip: (trip: Trip | null) => void

  // Real-time actions
  subscribeToTrip: (tripId: string) => void
  subscribeToUserTrips: (userId: string) => void
  subscribeToSquadTrips: (squadId: string) => void
  unsubscribeFromTrip: () => void
  unsubscribeFromUserTrips: () => void
  unsubscribeFromSquadTrips: () => void
  unsubscribeAll: () => void
}

/**
 * Trip store with Zustand
 */
export const useTripStore = create<TripState>((set, get) => ({
  // Initial state
  trips: [],
  currentTrip: null,
  loading: false,
  error: null,

  // Real-time subscriptions
  tripSubscription: null,
  userTripsSubscription: null,
  squadTripsSubscription: null,

  // Actions
  fetchUserTrips: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const trips = await TripService.getUserTrips(userId)
      set({ trips, loading: false })
    } catch (error) {
      console.error("Error fetching user trips:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchSquadTrips: async (squadId: string) => {
    try {
      set({ loading: true, error: null })
      const trips = await TripService.getSquadTrips(squadId)
      set({ trips, loading: false })
    } catch (error) {
      console.error("Error fetching squad trips:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchTrip: async (tripId: string) => {
    try {
      set({ loading: true, error: null })
      const trip = await TripService.getTrip(tripId)
      if (trip) {
        set({ currentTrip: trip, loading: false })
      } else {
        set({ error: new Error("Trip not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching trip:", error)
      set({ error: error as Error, loading: false })
    }
  },

  createTrip: async (tripData) => {
    try {
      set({ loading: true, error: null })
      const tripId = await TripService.createTrip(tripData)
      set({ loading: false })
      return tripId
    } catch (error) {
      console.error("Error creating trip:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  updateTrip: async (tripId, tripData) => {
    try {
      set({ loading: true, error: null })
      const result = await TripService.updateTrip(tripId, tripData)

      // If successful and we have the current trip loaded, update it
      if (result.success && get().currentTrip?.id === tripId) {
        const updatedTrip = await TripService.getTrip(tripId)
        if (updatedTrip) {
          set({ currentTrip: updatedTrip })
        }
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating trip:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  clearTrips: () => {
    set({ trips: [], currentTrip: null })
  },

  setCurrentTrip: (trip) => {
    set({ currentTrip: trip })
  },

  // Real-time actions
  subscribeToTrip: (tripId: string) => {
    // Unsubscribe from any existing subscription
    get().unsubscribeFromTrip()

    // Set loading state
    set({ loading: true, error: null })

    // Subscribe to real-time updates
    const unsubscribe = TripRealtimeService.subscribeToTrip(tripId, (trip, error) => {
      if (error) {
        set({ error: error, loading: false })
        return
      }

      set({ currentTrip: trip, loading: false })
    })

    // Store the unsubscribe function
    set({ tripSubscription: unsubscribe })
  },

  subscribeToUserTrips: (userId: string) => {
    // Unsubscribe from any existing subscription
    get().unsubscribeFromUserTrips()

    // Set loading state
    set({ loading: true, error: null })

    // Subscribe to real-time updates
    const unsubscribe = TripRealtimeService.subscribeToUserTrips(userId, (trips, error) => {
      if (error) {
        set({ error: error, loading: false })
        return
      }

      set({ trips, loading: false })
    })

    // Store the unsubscribe function
    set({ userTripsSubscription: unsubscribe })
  },

  subscribeToSquadTrips: (squadId: string) => {
    // Unsubscribe from any existing subscription
    get().unsubscribeFromSquadTrips()

    // Set loading state
    set({ loading: true, error: null })

    // Subscribe to real-time updates
    const unsubscribe = TripRealtimeService.subscribeToSquadTrips(squadId, (trips, error) => {
      if (error) {
        set({ error: error, loading: false })
        return
      }

      set({ trips, loading: false })
    })

    // Store the unsubscribe function
    set({ squadTripsSubscription: unsubscribe })
  },

  unsubscribeFromTrip: () => {
    const { tripSubscription } = get()
    if (tripSubscription) {
      tripSubscription()
      set({ tripSubscription: null })
    }
  },

  unsubscribeFromUserTrips: () => {
    const { userTripsSubscription } = get()
    if (userTripsSubscription) {
      userTripsSubscription()
      set({ userTripsSubscription: null })
    }
  },

  unsubscribeFromSquadTrips: () => {
    const { squadTripsSubscription } = get()
    if (squadTripsSubscription) {
      squadTripsSubscription()
      set({ squadTripsSubscription: null })
    }
  },

  unsubscribeAll: () => {
    get().unsubscribeFromTrip()
    get().unsubscribeFromUserTrips()
    get().unsubscribeFromSquadTrips()
  },
}))
