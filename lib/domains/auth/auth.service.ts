import { auth } from "@/lib/firebase"
import { signOut } from "firebase/auth"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"

/**
 * Auth service for Firebase authentication operations
 */
export class AuthService {
  /**
   * Log out the current user
   * @returns Service response indicating success or failure
   */
  static async logOut(): Promise<ServiceResponse> {
    try {
      await signOut(auth)
      return { success: true }
    } catch (error) {
      console.error("Error logging out:", error)
      return { success: false, error }
    }
  }

  /**
   * Get the current authentication token
   * @returns The authentication token or null if not authenticated
   */
  static async getAuthToken(): Promise<string | null> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return null
      }

      const token = await currentUser.getIdToken()
      return token
    } catch (error) {
      console.error("Error getting auth token:", error)
      return null
    }
  }
}
