import { Invitation } from "../firebase/invitation-service"
import { TransactionalEmailsApi, SendSmtpEmail } from "@getbrevo/brevo"
import { EmailTemplates } from "./email-templates"

// Configure the Brevo API client
const apiKey = process.env.BREVO_API_KEY

// Log warning if API key is not set
if (!apiKey) {
  console.warn("BREVO_API_KEY is not configured. Email sending will not work.")
}

// Initialize the Brevo API client
const apiInstance = new TransactionalEmailsApi()
apiInstance.setApiKey(0, apiKey || "")

/**
 * Send an email using the Brevo API
 */
export const sendEmail = async (options: {
  to: string
  subject: string
  templateId?: number
  params?: Record<string, any>
  htmlContent?: string
  from?: string
}) => {
  try {
    // Check if Brevo API key is configured
    if (!apiKey) {
      console.error("BREVO_API_KEY is not configured")
      return {
        success: false,
        error: "Email service is not properly configured",
      }
    }

    // Create the send email request
    const sendSmtpEmail = new SendSmtpEmail()

    // Set the sender
    sendSmtpEmail.sender = {
      name: "BroTrips.ai",
      email: options.from || process.env.EMAIL_FROM || "<EMAIL>",
    }

    // Set the recipient
    sendSmtpEmail.to = [{ email: options.to }]

    // Set the email content - either template or direct HTML
    if (options.templateId && options.params) {
      sendSmtpEmail.templateId = options.templateId
      sendSmtpEmail.params = options.params
    } else if (options.htmlContent) {
      sendSmtpEmail.subject = options.subject
      sendSmtpEmail.htmlContent = options.htmlContent
    } else {
      return {
        success: false,
        error: "Either templateId and params or htmlContent must be provided",
      }
    }

    try {
      // Send the email
      const data = await apiInstance.sendTransacEmail(sendSmtpEmail)

      // Extract messageId from response
      const messageId = data?.body?.messageId || `email-${Date.now()}`

      return {
        success: true,
        messageId,
      }
    } catch (sendError) {
      console.error("Error in Brevo API sendTransacEmail:", sendError)
      return {
        success: false,
        error: sendError instanceof Error ? sendError.message : "Error sending email via Brevo API",
      }
    }
  } catch (error) {
    console.error("Error sending email via Brevo:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}
/**
 * Generate an invitation link
 */
export const generateInvitationLink = (invitationId: string): string => {
  // Simply use the invitation ID as the token
  // The expiration will be checked in the invitation page based on the database record
  return `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/invitation/${invitationId}`
}

/**
 * Send an invitation email using a template
 */
export const sendInvitationEmail = async (
  invitation: Invitation,
  invitationLink: string,
  templateId?: number
) => {
  // If a template ID is provided or we have a default, use it
  const useTemplateId = templateId || EmailTemplates.INVITATION

  // Log template ID for debugging
  console.log("Using template ID:", useTemplateId)

  // Check if Brevo API key is configured
  if (!apiKey) {
    console.warn("BREVO_API_KEY is not configured. Falling back to direct HTML content.")
    // Skip template and use direct HTML
  } else if (useTemplateId) {
    return await sendEmail({
      to: invitation.inviteeEmail,
      subject: `You've been invited to join ${invitation.squadName} on BroTrips.ai`,
      templateId: useTemplateId,
      params: {
        squadName: invitation.squadName,
        inviterName: invitation.inviterName,
        invitationLink: invitationLink,
      },
    })
  }

  // Fallback to direct HTML content if no template is configured
  const htmlContent = `
    <h1>You've been invited to join ${invitation.squadName}</h1>
    <p>${invitation.inviterName} has invited you to join their squad on BroTrips.ai.</p>
    <p>Click the link below to accept or decline this invitation:</p>
    <p><a href="${invitationLink}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Invitation</a></p>
    <p>Or copy and paste this link into your browser:</p>
    <p>${invitationLink}</p>
    <p>Thanks,<br>The BroTrips.ai Team</p>
  `

  return await sendEmail({
    to: invitation.inviteeEmail,
    subject: `You've been invited to join ${invitation.squadName} on BroTrips.ai`,
    htmlContent,
  })
}
