// Define the allowed travel types that match the ones in travel-preferences.tsx
export const ALLOWED_TRAVEL_TYPES = [
  "Beach",
  "Mountains",
  "City",
  "Countryside",
  "Adventure",
  "Relaxation",
] as const

export type TravelType = (typeof ALLOWED_TRAVEL_TYPES)[number]

// Function to validate travel types
export function isValidTravelType(type: string): boolean {
  return ALLOWED_TRAVEL_TYPES.includes(type as TravelType)
}
