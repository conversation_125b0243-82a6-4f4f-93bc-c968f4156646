"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader2, DollarSign } from "lucide-react"
import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"

export function TravelPreferences() {
  const { preferences, loading: preferencesLoading, updatePreferences } = useUserPreferences()
  const user = useUser()

  // Local state
  const [selectedTravelTypes, setSelectedTravelTypes] = useState<string[]>(
    preferences?.travelPreferences || []
  )
  const [budgetRange, setBudgetRange] = useState<[number, number]>(
    Array.isArray(preferences?.budgetRange)
      ? (preferences.budgetRange as [number, number])
      : [500, 2000]
  )
  const [minBudget, setMinBudget] = useState<number>(budgetRange[0])
  const [maxBudget, setMaxBudget] = useState<number>(budgetRange[1])
  const [availability, setAvailability] = useState(
    preferences?.availabilityPreferences?.[0] || "weekends"
  )
  const [groupType, setGroupType] = useState(preferences?.travelGroupPreferences?.[0] || "friends")
  const [saving, setSaving] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Update local state when preferences change
  useEffect(() => {
    if (!preferencesLoading && preferences) {
      setSelectedTravelTypes(preferences.travelPreferences || [])
      const range: [number, number] = Array.isArray(preferences.budgetRange)
        ? (preferences.budgetRange as [number, number])
        : [500, 2000]
      setBudgetRange(range)
      setMinBudget(range[0])
      setMaxBudget(range[1])
      setAvailability(preferences.availabilityPreferences?.[0] || "weekends")
      setGroupType(preferences.travelGroupPreferences?.[0] || "friends")
      setInitialLoading(false)
    }
  }, [preferences, preferencesLoading])

  // Update budget range when min/max values change
  useEffect(() => {
    // Ensure max is always >= min
    const newMax = Math.max(minBudget, maxBudget)
    setBudgetRange([minBudget, newMax])
    if (newMax !== maxBudget) {
      setMaxBudget(newMax)
    }
  }, [minBudget, maxBudget])

  // Handle slider value changes
  const handleSliderChange = (value: number[]) => {
    const [min, max] = value as [number, number]
    setMinBudget(min)
    setMaxBudget(max)
  }

  const toggleTravelType = (type: string) => {
    // Check if the type already exists (case-insensitive)
    const typeExists = selectedTravelTypes.some((t) => t.toLowerCase() === type.toLowerCase())

    if (typeExists) {
      // Remove the type (case-insensitive)
      setSelectedTravelTypes(
        selectedTravelTypes.filter((t) => t.toLowerCase() !== type.toLowerCase())
      )
    } else {
      // Add the type with the exact case provided
      setSelectedTravelTypes([...selectedTravelTypes, type])
    }
  }

  const savePreferences = async () => {
    setSaving(true)
    try {
      await updatePreferences({
        travelPreferences: selectedTravelTypes,
        budgetRange: budgetRange,
        availabilityPreferences: [availability],
        travelGroupPreferences: [groupType],
      })
    } catch (error) {
      console.error("Error updating preferences:", error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Travel Preferences</CardTitle>
        <CardDescription>
          Update your travel preferences for better trip recommendations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {initialLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading travel preferences...</p>
          </div>
        ) : !user ? (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-sm text-muted-foreground mb-4">Unable to load travel preferences.</p>
            <p className="text-sm text-muted-foreground">
              Please sign in to view your travel preferences.
            </p>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <Label>Preferred Travel Types</Label>
              <div className="flex flex-wrap gap-2">
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "beach".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("Beach")}
                >
                  Beach
                </Badge>
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "mountains".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("Mountains")}
                >
                  Mountains
                </Badge>
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "city".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("City")}
                >
                  City
                </Badge>
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "countryside".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("Countryside")}
                >
                  Countryside
                </Badge>
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "adventure".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("Adventure")}
                >
                  Adventure
                </Badge>
                <Badge
                  variant={
                    selectedTravelTypes.some((t) => t.toLowerCase() === "relaxation".toLowerCase())
                      ? "default"
                      : "outline"
                  }
                  className="cursor-pointer hover:bg-primary/10"
                  onClick={() => toggleTravelType("Relaxation")}
                >
                  Relaxation
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Typical Budget Range (per person)</Label>
              <div className="pt-2 px-4">
                <div className="space-y-4">
                  <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                    <div className="flex-1 space-y-2">
                      <Label htmlFor="min-budget" className="text-xs">
                        Minimum
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="min-budget"
                          type="number"
                          min={100}
                          max={5000}
                          step={100}
                          value={minBudget}
                          onChange={(e) => {
                            const value = Math.max(
                              100,
                              Math.min(5000, parseInt(e.target.value) || 100)
                            )
                            setMinBudget(value)
                          }}
                          className="pl-9"
                        />
                      </div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <Label htmlFor="max-budget" className="text-xs">
                        Maximum
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="max-budget"
                          type="number"
                          min={minBudget}
                          max={5000}
                          step={100}
                          value={maxBudget}
                          onChange={(e) => {
                            const value = Math.max(
                              minBudget,
                              Math.min(5000, parseInt(e.target.value) || minBudget)
                            )
                            setMaxBudget(value)
                          }}
                          className="pl-9"
                        />
                      </div>
                    </div>
                  </div>
                  <Slider
                    value={budgetRange}
                    min={100}
                    max={5000}
                    step={100}
                    onValueChange={handleSliderChange}
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>$100</span>
                    <span>$5000</span>
                  </div>
                  <div className="flex justify-center text-sm font-medium">
                    <span>
                      Selected: ${minBudget}
                      {minBudget !== maxBudget ? ` - $${maxBudget}` : ""}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="availability">Typical Availability</Label>
                <Select value={availability} onValueChange={setAvailability}>
                  <SelectTrigger id="availability">
                    <SelectValue placeholder="Select availability" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekends">Weekends</SelectItem>
                    <SelectItem value="week-long">Week-long trips</SelectItem>
                    <SelectItem value="extended">Extended trips (2+ weeks)</SelectItem>
                    <SelectItem value="flexible">Flexible</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="group-type">Typical Group Type</Label>
                <Select value={groupType} onValueChange={setGroupType}>
                  <SelectTrigger id="group-type">
                    <SelectValue placeholder="Select group type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solo">Solo</SelectItem>
                    <SelectItem value="couple">Couple</SelectItem>
                    <SelectItem value="friends">Friends</SelectItem>
                    <SelectItem value="family">Family with Kids</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={savePreferences} disabled={saving || initialLoading || !user}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Preferences"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
