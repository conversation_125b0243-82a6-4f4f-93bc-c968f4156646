"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import {
  useIsUserSubscribed,
  useCanCreateMoreTripsInSquad,
  useUserSubscriptionWithInit,
} from "@/lib/domains/user-subscription/user-subscription.hooks"
import { createTrip, getUserSquads, type Squad } from "@/lib/firebase-service"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CalendarIcon, Loader2, ImageIcon, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { getLocationImageByPlaceId } from "@/lib/google-places"
import { OptimizedImage } from "@/components/optimized-image"
import { DestinationAutocomplete } from "./components/destination-autocomplete"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Link from "next/link"
import { SubscriptionErrorType, getSubscriptionErrorAlert } from "@/lib/subscription-errors"

interface TripFormData {
  name: string
  destination: string
  placeId?: string
  squadId: string
  startDate: Date | null
  endDate: Date | null
  budget: string
  description: string
}

export default function CreateTripPage() {
  const user = useUser()
  const isSubscribed = useIsUserSubscribed()
  const canCreateMoreTripsInSquadFunc = useCanCreateMoreTripsInSquad()
  const { handleSubscriptionError } = useUserSubscriptionWithInit()
  const router = useRouter()
  const { toast } = useToast()
  const [squads, setSquads] = useState<Squad[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [selectedSquadId, setSelectedSquadId] = useState<string>("")
  const [canCreateTrip, setCanCreateTrip] = useState(true)
  const [checkingLimits, setCheckingLimits] = useState(false)
  const [locationImage, setLocationImage] = useState<string | null>(null)
  const [imageAttribution, setImageAttribution] = useState<{
    name: string
    photoReference?: string
    username?: string
    link?: string
  } | null>(null)
  const [loadingImage, setLoadingImage] = useState(false)
  const [lastFetchedLocation, setLastFetchedLocation] = useState<{
    placeId: string
    name: string
  } | null>(null)
  const [formData, setFormData] = useState<TripFormData>({
    name: "",
    destination: "",
    placeId: undefined,
    squadId: "",
    startDate: null,
    endDate: null,
    budget: "",
    description: "",
  })

  useEffect(() => {
    if (!user) {
      router.push("/login")
      return
    }

    const fetchSquads = async () => {
      try {
        const squadsList = await getUserSquads(user.uid)
        setSquads(squadsList || [])
        setLoading(false)
      } catch (error) {
        console.error("Error fetching squads:", error)
        toast({
          title: "Error",
          description: "Failed to load squads. Please try again.",
          variant: "destructive",
        })
        setLoading(false)
      }
    }

    fetchSquads()
  }, [user, router, toast])

  // Check if user can create more trips in the selected squad
  useEffect(() => {
    const checkTripLimits = async () => {
      if (!user || !selectedSquadId || isSubscribed) {
        setCanCreateTrip(true)
        return
      }

      try {
        setCheckingLimits(true)
        const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, selectedSquadId)
        setCanCreateTrip(canCreate)
      } catch (error) {
        console.error("Error checking trip limits:", error)
        // Default to allowing creation, but log the error
        setCanCreateTrip(true)
      } finally {
        setCheckingLimits(false)
      }
    }

    if (selectedSquadId) {
      checkTripLimits()
    }
  }, [user, selectedSquadId, isSubscribed, canCreateMoreTripsInSquadFunc])

  const handleChange = (field: keyof TripFormData, value: any, placeId?: string) => {
    // If this is a destination change with a place ID, update both fields
    if (field === "destination") {
      setFormData((prev) => ({
        ...prev,
        destination: value,
        placeId: placeId,
      }))

      // Only fetch image if a place ID is provided (meaning user clicked an autocomplete suggestion)
      // This prevents loading images for every keystroke
      if (placeId) {
        fetchLocationImageByPlaceId(placeId, value)
      } else if (value !== formData.destination) {
        // If user is typing manually (no placeId) and the value has changed,
        // clear the current image since it no longer matches the location
        if (locationImage) {
          setLocationImage(null)
          setImageAttribution(null)
          setLastFetchedLocation(null)
        }
      }
    } else if (field === "squadId") {
      // Update the selected squad ID for subscription limit checking
      setSelectedSquadId(value)
      setFormData((prev) => ({ ...prev, [field]: value }))
    } else {
      // For other fields, just update the value
      setFormData((prev) => ({ ...prev, [field]: value }))
    }
  }

  const fetchLocationImageByPlaceId = async (placeId: string, locationName: string) => {
    // Skip fetching if we've already fetched this location
    if (
      lastFetchedLocation &&
      lastFetchedLocation.placeId === placeId &&
      lastFetchedLocation.name === locationName
    ) {
      return
    }

    try {
      console.log("Starting fetchLocationImageByPlaceId with:", { placeId, locationName })
      setLoadingImage(true)
      const imageResult = await getLocationImageByPlaceId(placeId, locationName)
      console.log("Image result from getLocationImageByPlaceId:", imageResult)
      setLocationImage(imageResult.url)
      if (imageResult.attribution) {
        setImageAttribution(imageResult.attribution)
      }

      // Save this location as the last one we fetched
      setLastFetchedLocation({
        placeId,
        name: locationName,
      })
    } catch (error) {
      console.error("Error fetching location image by place ID:", error)
      setLocationImage(null)
      setImageAttribution(null)
    } finally {
      setLoadingImage(false)
    }
  }

  // We no longer need a separate fetchLocationImage function since we only load images
  // when autocomplete suggestions are clicked, which always provides a placeId

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    if (
      !formData.name ||
      !formData.destination ||
      !formData.squadId ||
      !formData.startDate ||
      !formData.endDate
    ) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // Validate that startDate is in the future (not today or earlier)
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison

    if (formData.startDate && formData.startDate <= today) {
      toast({
        title: "Invalid start date",
        description: "Start date must be in the future (not today or earlier).",
        variant: "destructive",
      })
      return
    }

    // Double-check subscription limits before creating
    if (!isSubscribed) {
      const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, formData.squadId)
      if (!canCreate) {
        // Use centralized error handling
        handleSubscriptionError(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
        return
      }
    }

    try {
      setSubmitting(true)

      // Get the selected squad
      const squad = squads.find((s) => s.id === formData.squadId)
      if (!squad) {
        throw new Error("Selected squad not found")
      }

      // Create the trip
      const tripId = await createTrip({
        name: formData.name,
        destination: formData.destination,
        squadId: formData.squadId,
        startDate: formData.startDate as any,
        endDate: formData.endDate as any,
        budget: formData.budget,
        description: formData.description,
        // Include place ID, locationThumbnail, and imageAttribution if available
        ...(formData.placeId ? { placeId: formData.placeId } : {}),
        ...(locationImage ? { locationThumbnail: locationImage } : {}),
        ...(imageAttribution ? { imageAttribution } : {}),
        status: "planning",
        attendees: squad.members,
        leaderId: user.uid,
        createdBy: user.uid,
      })

      toast({
        title: "Trip created!",
        description: "Your trip has been created successfully.",
      })

      router.push(`/trips/${tripId}`)
    } catch (error) {
      console.error("Error creating trip:", error)
      toast({
        title: "Error",
        description: "Failed to create trip. Please try again.",
        variant: "destructive",
      })
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <AppHeader />
        <div className="flex-1 flex">
          <AppSidebar />
          <div className="flex-1 p-6 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-3xl font-bold mb-6">Create a New Trip</h1>

            {selectedSquadId && !canCreateTrip && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>
                  {
                    getSubscriptionErrorAlert(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
                      .title
                  }
                </AlertTitle>
                <AlertDescription>
                  {
                    getSubscriptionErrorAlert(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
                      .description
                  }
                  <div className="mt-2">
                    <Link href="/settings?tab=billing" className="underline font-medium">
                      Upgrade to Pro
                    </Link>{" "}
                    to create unlimited trips.
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Trip Details</CardTitle>
                <CardDescription>Fill in the details for your new trip</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Trip Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleChange("name", e.target.value)}
                        placeholder="Summer Vacation 2023"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <DestinationAutocomplete
                        value={formData.destination}
                        onChange={(value, placeId) => handleChange("destination", value, placeId)}
                        placeholder="Miami, Florida"
                        required
                      />

                      {/* Location Image Preview - only show when we have a valid destination with placeId */}
                      {formData.destination && formData.placeId && (
                        <div className="mt-2 space-y-1">
                          <div className="rounded-lg overflow-hidden">
                            {loadingImage ? (
                              <div className="aspect-video bg-muted flex items-center justify-center">
                                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                              </div>
                            ) : locationImage ? (
                              <OptimizedImage
                                src={locationImage}
                                alt={formData.destination}
                                aspectRatio="video"
                                className="rounded-lg"
                                priority
                                quality={90}
                              />
                            ) : (
                              <div className="aspect-video bg-muted flex items-center justify-center">
                                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                                <span className="ml-2 text-muted-foreground">
                                  No image available
                                </span>
                              </div>
                            )}
                          </div>
                          {imageAttribution && (
                            <div className="text-xs text-muted-foreground text-right">
                              {imageAttribution.link ? (
                                <>
                                  Photo by{" "}
                                  <a
                                    href={imageAttribution.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                  >
                                    {imageAttribution.name}
                                  </a>{" "}
                                  on{" "}
                                  <a
                                    href="https://unsplash.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                  >
                                    Unsplash
                                  </a>
                                </>
                              ) : (
                                <>Photo of {imageAttribution.name} via Google Places</>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="squad">Squad</Label>
                      <Select
                        value={formData.squadId}
                        onValueChange={(value) => handleChange("squadId", value)}
                        required
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a squad" />
                        </SelectTrigger>
                        <SelectContent>
                          {squads.length > 0 ? (
                            squads.map((squad) => (
                              <SelectItem key={squad.id} value={squad.id}>
                                {squad.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="none" disabled>
                              No squads available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Start Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !formData.startDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {formData.startDate ? (
                                format(formData.startDate, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={formData.startDate || undefined}
                              onSelect={(date) => handleChange("startDate", date)}
                              initialFocus
                              disabled={(date) => {
                                // Disable today and past dates
                                const today = new Date()
                                today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison
                                return date <= today
                              }}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div className="space-y-2">
                        <Label>End Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !formData.endDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {formData.endDate ? (
                                format(formData.endDate, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={formData.endDate || undefined}
                              onSelect={(date) => handleChange("endDate", date)}
                              initialFocus
                              disabled={(date) => {
                                // Get today's date with time set to start of day
                                const today = new Date()
                                today.setHours(0, 0, 0, 0)

                                // If startDate is selected, disable dates before startDate
                                // Otherwise, disable today and past dates
                                return (
                                  (formData.startDate
                                    ? date < formData.startDate
                                    : date <= today) || date < new Date("1900-01-01")
                                )
                              }}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="budget">Budget (USD)</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                          $
                        </span>
                        <Input
                          id="budget"
                          value={
                            formData.budget.startsWith("$")
                              ? formData.budget.substring(1)
                              : formData.budget
                          }
                          onChange={(e) => {
                            // Only allow numeric inputs
                            const value = e.target.value.replace(/[^0-9]/g, "")
                            handleChange("budget", `${value}`)
                          }}
                          className="pl-6"
                          placeholder="1000"
                          type="number"
                          min="0"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">Description (optional)</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => handleChange("description", e.target.value)}
                        placeholder="Add some details about this trip..."
                        rows={4}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={submitting || (selectedSquadId && !canCreateTrip) || checkingLimits}
                    >
                      {(submitting || checkingLimits) && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Create Trip
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
