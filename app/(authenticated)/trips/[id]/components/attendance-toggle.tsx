"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { User<PERSON>he<PERSON>, UserX, HelpCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { updateUserTripStatus, UserTrip } from "@/lib/firebase-service"
import { checkUserTripStatus } from "@/lib/firebase/user-trip-service"
import { InlineLoading } from "@/components/inline-loading"

interface TripAttendanceToggleProps {
  tripId: string
  onStatusChange?: (status: "going" | "not-going" | "undecided") => void
}

export function TripAttendanceToggle({ tripId, onStatusChange }: TripAttendanceToggleProps) {
  const user = useUser()
  const { toast } = useToast()
  const [userTripStatus, setUserTripStatus] = useState<UserTrip | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    const fetchUserTripStatus = async () => {
      if (!user) return

      try {
        setLoading(true)
        // Check if status exists first
        const existingStatus = await checkUserTripStatus(user.uid, tripId)

        if (existingStatus) {
          setUserTripStatus(existingStatus)
        } else {
          // Only create a status for the current user
          await updateUserTripStatus(user.uid, tripId, "not-going")
          // Fetch the newly created status
          const newStatus = await checkUserTripStatus(user.uid, tripId)
          if (newStatus) {
            setUserTripStatus(newStatus)
          }
        }
      } catch (error) {
        console.error("Error fetching user trip status:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserTripStatus()
  }, [user, tripId])

  const handleStatusChange = async (status: "going" | "not-going" | "undecided") => {
    if (!user) return

    try {
      setUpdating(true)
      await updateUserTripStatus(user.uid, tripId, status)

      // Update local state
      if (userTripStatus) {
        setUserTripStatus({
          ...userTripStatus,
          status,
        })
      }

      // Notify parent component
      if (onStatusChange) {
        onStatusChange(status)
      }

      let title = "Status updated"
      let description = "Your attendance status has been updated"

      if (status === "going") {
        title = "You're going!"
        description = "You've been added to the trip attendees"
      } else if (status === "not-going") {
        description = "You've been marked as not attending this trip"
      } else if (status === "undecided") {
        description = "You've been marked as undecided for this trip"
      }

      toast({ title, description })
    } catch (error) {
      console.error("Error updating trip status:", error)
      toast({
        title: "Error",
        description: "Failed to update your attendance status",
        variant: "destructive",
      })
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trip Attendance</CardTitle>
          <CardDescription>Loading your status...</CardDescription>
        </CardHeader>
        <CardContent>
          <InlineLoading size="medium" />
        </CardContent>
      </Card>
    )
  }

  const isGoing = userTripStatus?.status === "going"
  const isNotGoing = userTripStatus?.status === "not-going"
  const isUndecided = userTripStatus?.status === "undecided"

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trip Attendance</CardTitle>
        <CardDescription>
          {isGoing
            ? "You're attending this trip"
            : isUndecided
              ? "You're undecided about this trip"
              : "Are you attending this trip?"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant={isGoing ? "default" : "outline"}
            className="flex-1"
            onClick={() => handleStatusChange("going")}
            disabled={updating || isGoing}
          >
            <UserCheck className="mr-2 h-4 w-4" />
            {isGoing ? "Going" : "I'm Going"}
          </Button>
          <Button
            variant={isUndecided ? "default" : "outline"}
            className="flex-1"
            onClick={() => handleStatusChange("undecided")}
            disabled={updating || isUndecided}
          >
            <HelpCircle className="mr-2 h-4 w-4" />
            {isUndecided ? "Undecided" : "Not Sure Yet"}
          </Button>
          <Button
            variant={isNotGoing ? "default" : "outline"}
            className="flex-1"
            onClick={() => handleStatusChange("not-going")}
            disabled={updating || isNotGoing}
          >
            <UserX className="mr-2 h-4 w-4" />
            {isNotGoing ? "Not Going" : "I'm Not Going"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
