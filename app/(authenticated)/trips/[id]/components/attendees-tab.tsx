"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { User, Trip } from "@/lib/firebase-service"
import { checkUserTripStatus } from "@/lib/firebase/user-trip-service"
import { isUserSubscribed } from "@/lib/firebase/subscription-service"
import { Check, X, HelpCircle } from "lucide-react"
import { SubscriberBadge } from "@/components/subscriber-badge"
import { UserDisplay } from "@/components/user-display"

interface AttendeesTabProps {
  trip: Trip
  attendees: User[]
  squadMembers: User[]
  currentUserId: string
}

export function AttendeesTab({ trip, attendees, squadMembers, currentUserId }: AttendeesTabProps) {
  const [attendanceStatus, setAttendanceStatus] = useState<Record<string, string>>({})
  const [subscriptionStatus, setSubscriptionStatus] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)

  // State to track all users (squad members + any additional attendees)
  const [allUsers, setAllUsers] = useState<User[]>([])

  // Combine squad members and attendees, removing duplicates
  useEffect(() => {
    const userMap = new Map<string, User>()

    // Add squad members first
    squadMembers.forEach((member) => {
      userMap.set(member.uid, member)
    })

    // Add any attendees that might not be in the squad
    attendees.forEach((attendee) => {
      if (!userMap.has(attendee.uid)) {
        userMap.set(attendee.uid, attendee)
      }
    })

    setAllUsers(Array.from(userMap.values()))
  }, [squadMembers, attendees])

  useEffect(() => {
    const fetchAttendanceStatuses = async () => {
      setLoading(true)
      const statuses: Record<string, string> = {}
      const subscriptions: Record<string, boolean> = {}

      // Process in batches to avoid too many concurrent requests
      const batchSize = 5
      for (let i = 0; i < allUsers.length; i += batchSize) {
        const batch = allUsers.slice(i, i + batchSize)

        // Fetch attendance statuses
        const statusPromises = batch.map(async (user) => {
          try {
            const status = await checkUserTripStatus(user.uid, trip.id)
            // If no status is found, mark as undecided instead of pending
            return { uid: user.uid, status: status ? status.status : "undecided" }
          } catch (error) {
            console.error(`Error fetching status for ${user.uid}:`, error)
            return { uid: user.uid, status: "undecided" }
          }
        })

        // Fetch subscription statuses for all users
        const subscriptionPromises = batch.map(async (user) => {
          try {
            // Check subscription status for all users
            const isSubscribed = await isUserSubscribed(user.uid)
            console.log({ uid: user.uid, isSubscribed })
            return { uid: user.uid, isSubscribed }
          } catch (error) {
            console.error(`Error fetching subscription for ${user.uid}:`, error)
            return { uid: user.uid, isSubscribed: false }
          }
        })

        const statusResults = await Promise.all(statusPromises)
        const subscriptionResults = await Promise.all(subscriptionPromises)

        // Update the status records
        statusResults.forEach((result) => {
          statuses[result.uid] = result.status
        })

        // Update the subscription records
        subscriptionResults.forEach((result) => {
          subscriptions[result.uid] = result.isSubscribed
        })
      }

      setAttendanceStatus(statuses)
      setSubscriptionStatus(subscriptions)
      setLoading(false)
    }

    if (allUsers.length > 0) {
      fetchAttendanceStatuses()
    }
  }, [allUsers, trip.id, currentUserId])

  // Filter all users by status
  const going = allUsers.filter((user) => attendanceStatus[user.uid] === "going")
  const notGoing = allUsers.filter((user) => attendanceStatus[user.uid] === "not-going")
  const undecided = allUsers.filter(
    (user) =>
      !attendanceStatus[user.uid] ||
      attendanceStatus[user.uid] === "undecided" ||
      attendanceStatus[user.uid] === "pending"
  )

  // Sort the all users list to prioritize going users
  const sortedAllUsers = [...allUsers].sort((a, b) => {
    // Going users first
    if (attendanceStatus[a.uid] === "going" && attendanceStatus[b.uid] !== "going") return -1
    if (attendanceStatus[a.uid] !== "going" && attendanceStatus[b.uid] === "going") return 1
    // Then undecided users
    if (attendanceStatus[a.uid] === "undecided" && attendanceStatus[b.uid] === "not-going")
      return -1
    if (attendanceStatus[a.uid] === "not-going" && attendanceStatus[b.uid] === "undecided") return 1
    // Alphabetical by name as a final sort
    return (a.displayName || "").localeCompare(b.displayName || "")
  })

  const renderAttendeeList = (userList: User[]) => (
    <div className="space-y-4 mt-4 max-h-[60vh] overflow-y-auto pr-2">
      {userList.length === 0 ? (
        <p className="text-muted-foreground text-sm">No users in this category</p>
      ) : (
        userList.map((attendee) => (
          <div
            key={attendee.uid}
            className="flex items-center justify-between p-3 rounded-lg border bg-card"
          >
            <div className="flex items-center space-x-3 overflow-hidden">
              <div className="min-w-0 flex-1 overflow-hidden">
                <div className="flex flex-wrap items-center gap-2">
                  <UserDisplay
                    displayName={attendee.displayName}
                    photoURL={attendee.photoURL}
                    showBadge={false} /* We'll show the badge separately */
                  />
                  <div className="flex flex-wrap gap-2">
                    {attendee.uid === trip.leaderId && (
                      <Badge variant="outline" className="text-xs whitespace-nowrap">
                        Trip Leader
                      </Badge>
                    )}
                    {subscriptionStatus[attendee.uid] && (
                      <div className="flex items-center bg-amber-50 dark:bg-amber-950 px-2 py-0.5 rounded-full">
                        <SubscriberBadge />
                        <span className="text-xs ml-1 text-amber-600 dark:text-amber-400 font-medium">
                          Pro
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground truncate ml-10">{attendee.email}</p>
              </div>
            </div>

            {/* Status indicator */}
            <div className="flex items-center shrink-0 ml-2">
              {attendanceStatus[attendee.uid] === "going" && (
                <Badge
                  variant="outline"
                  className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800 whitespace-nowrap"
                >
                  <Check className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Going</span>
                </Badge>
              )}
              {attendanceStatus[attendee.uid] === "not-going" && (
                <Badge
                  variant="outline"
                  className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800 whitespace-nowrap"
                >
                  <X className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Not Going</span>
                </Badge>
              )}
              {(attendanceStatus[attendee.uid] === "undecided" ||
                !attendanceStatus[attendee.uid] ||
                attendanceStatus[attendee.uid] === "pending") && (
                <Badge
                  variant="outline"
                  className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800 whitespace-nowrap"
                >
                  <HelpCircle className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Undecided</span>
                </Badge>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendees</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-12 w-12 bg-muted rounded-full mb-2"></div>
              <div className="h-4 w-32 bg-muted rounded mb-2"></div>
              <div className="h-3 w-24 bg-muted rounded"></div>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="all" className="w-full">
            <div className="overflow-x-auto pb-2 no-scrollbar">
              <TabsList className="w-full md:w-auto inline-flex md:grid md:grid-cols-4">
                <TabsTrigger value="all" className="whitespace-nowrap">
                  All ({allUsers.length})
                </TabsTrigger>
                <TabsTrigger value="going" className="whitespace-nowrap">
                  Going ({going.length})
                </TabsTrigger>
                <TabsTrigger value="undecided" className="whitespace-nowrap">
                  Undecided ({undecided.length})
                </TabsTrigger>
                <TabsTrigger value="not-going" className="whitespace-nowrap">
                  Not Going ({notGoing.length})
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all">{renderAttendeeList(sortedAllUsers)}</TabsContent>

            <TabsContent value="going">{renderAttendeeList(going)}</TabsContent>

            <TabsContent value="undecided">{renderAttendeeList(undecided)}</TabsContent>

            <TabsContent value="not-going">{renderAttendeeList(notGoing)}</TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
