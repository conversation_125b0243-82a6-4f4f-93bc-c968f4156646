"use client"

import { TripExpensesTab as ExpensesTabComponent } from "./expenses/expenses-tab"
import { Trip } from "@/lib/domains/trip/trip.types"

interface TripExpensesTabProps {
  trip: Trip
  attendees: any[]
}

export function TripExpensesTab({ trip, attendees }: TripExpensesTabProps) {
  // This is now just a wrapper that imports the refactored component
  return <ExpensesTabComponent trip={trip} attendees={attendees} />
}
