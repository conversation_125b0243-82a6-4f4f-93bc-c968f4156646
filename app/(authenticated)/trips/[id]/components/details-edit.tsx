"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Trip, updateTrip } from "@/lib/firebase-service"
import { Timestamp } from "firebase/firestore"
import { Check, X } from "lucide-react"

interface TripDetailsEditProps {
  trip: Trip
  onCancel: () => void
  onSave: (updatedTrip: Trip) => void
}

export function TripDetailsEdit({ trip, onCancel, onSave }: TripDetailsEditProps) {
  const { toast } = useToast()
  const [name, setName] = useState(trip.name)
  const [destination, setDestination] = useState(trip.destination)
  const [description, setDescription] = useState(trip.description || "")
  const [budget, setBudget] = useState<string>(
    typeof trip.budget === "string" ? trip.budget : `$${trip.budget}`
  )
  const [startDate, setStartDate] = useState(
    trip.startDate ? trip.startDate.toDate().toISOString().split("T")[0] : ""
  )
  const [endDate, setEndDate] = useState(
    trip.endDate ? trip.endDate.toDate().toISOString().split("T")[0] : ""
  )
  const [status, setStatus] = useState<
    "planning" | "upcoming" | "active" | "completed" | "cancelled"
  >(trip.status)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!name || !destination || !startDate || !endDate) {
      toast({
        title: "Missing fields",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    // Validate budget is a number
    const budgetStr = typeof budget === "string" ? budget.replace(/[^0-9.]/g, "") : String(budget)
    const budgetNum = Number(budgetStr)
    if (isNaN(budgetNum) || budgetNum <= 0) {
      toast({
        title: "Invalid budget",
        description: "Please enter a valid budget amount",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Convert dates to Firebase Timestamp format
      // Ensure budget is properly formatted as a string with $ prefix
      const formattedBudget =
        typeof budget === "string" && budget.startsWith("$")
          ? budget
          : `$${typeof budget === "string" ? budget.replace(/[^0-9.]/g, "") : budget}`

      const updatedTripData: Partial<Trip> = {
        name,
        destination,
        description,
        budget: formattedBudget,
        status: status as Trip["status"],
      }

      // Add dates if they are provided
      if (startDate) {
        updatedTripData.startDate = Timestamp.fromDate(new Date(startDate))
      }

      if (endDate) {
        updatedTripData.endDate = Timestamp.fromDate(new Date(endDate))
      }

      await updateTrip(trip.id, updatedTripData)

      toast({
        title: "Trip updated",
        description: "Trip details have been updated successfully",
      })

      onSave({
        ...trip,
        ...updatedTripData,
      })
    } catch (error) {
      console.error("Error updating trip:", error)
      toast({
        title: "Error",
        description: "Failed to update trip details. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Edit Trip Details</CardTitle>
        <CardDescription>Update information about your trip</CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Trip Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter trip name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="destination">Destination</Label>
            <Input
              id="destination"
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              placeholder="Enter destination"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                min={startDate}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="budget">Budget (USD)</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  $
                </span>
                <Input
                  id="budget"
                  type="number"
                  value={
                    typeof budget === "string" && budget.startsWith("$")
                      ? budget.substring(1)
                      : budget
                  }
                  onChange={(e) => {
                    // Only allow numeric inputs
                    const value = e.target.value.replace(/[^0-9]/g, "")
                    setBudget(`${value}`)
                  }}
                  className="pl-6"
                  placeholder="1000"
                  min="0"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Trip Status</Label>
              <Select value={status} onValueChange={(value) => setStatus(value as Trip["status"])}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter trip description"
              rows={4}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            <X className="mr-2 h-4 w-4" /> Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </div>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" /> Save Changes
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
