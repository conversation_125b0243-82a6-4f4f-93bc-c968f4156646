"use client"

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plane, Hotel, Star } from "lucide-react"

interface TripBookingInfoProps {
  destination: string
  attendeeCount: number
  startDate?: Date
  endDate?: Date
}

export function TripBookingInfo({
  destination,
  attendeeCount,
  startDate,
  endDate,
}: TripBookingInfoProps) {
  // In a real implementation, this would fetch actual booking options from an API
  // For now, we'll use mock data

  return (
    <Card>
      <CardHeader>
        <CardTitle>Booking Information</CardTitle>
        <CardDescription>Travel and accommodation details</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Plane className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">Flights</h3>
                <p className="text-sm text-muted-foreground">Recommended options for your group</p>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <div className="p-3 border-b bg-muted/50">
                <h4 className="font-medium">Outbound Flight</h4>
              </div>
              <div className="p-3 space-y-2">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Delta Airlines</p>
                    <p className="text-sm text-muted-foreground">
                      DL1234 • {startDate ? startDate.toLocaleDateString() : "Date TBD"}
                    </p>
                  </div>
                  <p className="font-medium">$320</p>
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <p>SFO 10:00 AM</p>
                  <div className="flex-1 mx-2 border-t border-dashed"></div>
                  <p>{getAirportCode(destination)} 1:30 PM</p>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-2">
                  View Details
                </Button>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <div className="p-3 border-b bg-muted/50">
                <h4 className="font-medium">Return Flight</h4>
              </div>
              <div className="p-3 space-y-2">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Delta Airlines</p>
                    <p className="text-sm text-muted-foreground">
                      DL5678 • {endDate ? endDate.toLocaleDateString() : "Date TBD"}
                    </p>
                  </div>
                  <p className="font-medium">$340</p>
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <p>{getAirportCode(destination)} 2:15 PM</p>
                  <div className="flex-1 mx-2 border-t border-dashed"></div>
                  <p>SFO 5:45 PM</p>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-2">
                  View Details
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Hotel className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">Accommodation</h3>
                <p className="text-sm text-muted-foreground">Lodging options for your stay</p>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <div className="aspect-video relative">
                <img
                  src="/placeholder.svg?height=150&width=300"
                  alt="Accommodation"
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-3 space-y-2">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">{getAccommodationName(destination)}</h4>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < 4 ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                      />
                    ))}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Spacious accommodation with multiple bedrooms, perfect for your group of{" "}
                  {attendeeCount}.
                </p>
                <div className="flex justify-between items-center pt-2">
                  <p className="font-medium">$250/night</p>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            </div>

            <div className="rounded-md border p-3 space-y-2">
              <h4 className="font-medium">Alternative Options</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 rounded-md bg-muted/50">
                  <div>
                    <p className="font-medium">{getAlternativeAccommodation(destination, 1)}</p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${i < 5 ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="font-medium">$320/night</p>
                </div>
                <div className="flex justify-between items-center p-2 rounded-md bg-muted/50">
                  <div>
                    <p className="font-medium">{getAlternativeAccommodation(destination, 2)}</p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${i < 3 ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="font-medium">$180/night</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full">Book Now</Button>
      </CardFooter>
    </Card>
  )
}

// Helper functions to generate mock data based on destination
function getAirportCode(destination: string): string {
  const destinationMap: Record<string, string> = {
    "New York": "JFK",
    "Los Angeles": "LAX",
    Chicago: "ORD",
    Miami: "MIA",
    "Las Vegas": "LAS",
    "San Francisco": "SFO",
    Denver: "DEN",
    Seattle: "SEA",
    Boston: "BOS",
    Orlando: "MCO",
    Nashville: "BNA",
    Austin: "AUS",
    "New Orleans": "MSY",
    Portland: "PDX",
    "San Diego": "SAN",
    "Washington DC": "DCA",
    Philadelphia: "PHL",
    Atlanta: "ATL",
    Dallas: "DFW",
    Houston: "IAH",
    Phoenix: "PHX",
    "Salt Lake City": "SLC",
    Honolulu: "HNL",
    Anchorage: "ANC",
    "Jackson Hole": "JAC",
    Yellowstone: "WYS",
    "Grand Canyon": "GCN",
    Yosemite: "FAT", // Fresno is closest to Yosemite
    "Lake Tahoe": "RNO", // Reno is closest to Lake Tahoe
  }

  // Try to match the destination to a known airport code
  for (const [city, code] of Object.entries(destinationMap)) {
    if (destination.toLowerCase().includes(city.toLowerCase())) {
      return code
    }
  }

  // If no match, return a generic code
  return "DST"
}

function getAccommodationName(destination: string): string {
  const accommodationPrefixes = [
    "Mountain View",
    "Lakeside",
    "Downtown",
    "Beachfront",
    "Riverside",
    "Luxury",
    "Cozy",
    "Grand",
    "Historic",
    "Modern",
  ]

  const accommodationSuffixes = [
    "Lodge",
    "Resort",
    "Hotel",
    "Suites",
    "Inn",
    "Retreat",
    "Cabins",
    "Villas",
    "Apartments",
    "Hideaway",
  ]

  // Generate a name based on the destination
  const prefix = accommodationPrefixes[Math.floor(Math.random() * accommodationPrefixes.length)]
  const suffix = accommodationSuffixes[Math.floor(Math.random() * accommodationSuffixes.length)]

  // For some destinations, use a more specific name
  if (
    destination.toLowerCase().includes("beach") ||
    destination.toLowerCase().includes("coast") ||
    destination.toLowerCase().includes("ocean")
  ) {
    return "Oceanview " + suffix
  }

  if (
    destination.toLowerCase().includes("mountain") ||
    destination.toLowerCase().includes("ski") ||
    destination.toLowerCase().includes("alps")
  ) {
    return "Mountain " + suffix
  }

  if (destination.toLowerCase().includes("lake") || destination.toLowerCase().includes("river")) {
    return "Waterfront " + suffix
  }

  if (
    destination.toLowerCase().includes("city") ||
    destination.toLowerCase().includes("york") ||
    destination.toLowerCase().includes("angeles") ||
    destination.toLowerCase().includes("francisco")
  ) {
    return "Downtown " + suffix
  }

  // Default to a generic name
  return prefix + " " + suffix
}

function getAlternativeAccommodation(destination: string, index: number): string {
  const alternatives = [
    `${destination} Resort`,
    `${destination} Grand Hotel`,
    `${destination} Wilderness Cabins`,
    `${destination} Luxury Suites`,
    `${destination} Budget Inn`,
    `${destination} Historic Hotel`,
  ]

  return alternatives[index % alternatives.length]
}
