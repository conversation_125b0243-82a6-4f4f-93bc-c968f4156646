"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import {
  fetchWeatherForecast,
  formatWeatherData,
  getWeatherEmoji,
  type WeatherDay,
} from "@/lib/weather-service"
import { InlineLoading } from "@/components/inline-loading"

interface TripWeatherWidgetProps {
  destination: string
  startDate?: Date
  endDate?: Date
}

export function TripWeatherWidget({ destination, startDate, endDate }: TripWeatherWidgetProps) {
  const [weatherData, setWeatherData] = useState<WeatherDay[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)

  useEffect(() => {
    async function loadWeatherData() {
      if (!destination) {
        setError("No destination provided")
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        setMessage(null)

        // Get the maximum forecast data available (14 days from today)
        const forecast = await fetchWeatherForecast(destination, 14)

        if (!forecast) {
          setError("Weather forecast is not available right now.")
          setLoading(false)
          return
        }

        // If no start date is provided, just show the next 7 days
        if (!startDate) {
          const formattedData = formatWeatherData(forecast)
          setWeatherData(formattedData.slice(0, 7))
          setLoading(false)
          return
        }

        // Format the trip start date to match the API date format (YYYY-MM-DD)
        const tripStartDateStr = startDate.toISOString().split("T")[0]

        // Check if any of the forecast days match or are after the trip start date
        console.log(forecast.forecast)
        const forecastDaysForTrip = forecast.forecast.forecastday.filter(
          (day) => day.date >= tripStartDateStr
        )

        // If no forecast days match the trip dates, the trip start date is beyond the forecast range
        if (forecastDaysForTrip.length === 0) {
          setMessage("Weather forecast is not available yet as your trip is more than 3 days away.")
          setLoading(false)
          return
        }

        // Calculate how many days to show (max 7 days, not exceeding trip end date)
        let maxDaysToShow = 7

        if (endDate) {
          // If we have an end date, calculate how many days the trip lasts
          const tripEndDateStr = endDate.toISOString().split("T")[0]

          // Filter forecast days to be between start and end dates
          const daysWithinTripRange = forecastDaysForTrip.filter(
            (day) => day.date <= tripEndDateStr
          )

          // Limit to maximum 7 days
          const filteredForecast = {
            ...forecast,
            forecast: {
              forecastday: daysWithinTripRange.slice(0, maxDaysToShow),
            },
          }

          const formattedData = formatWeatherData(filteredForecast)
          setWeatherData(formattedData)
        } else {
          // No end date, just show up to 7 days from start date
          const filteredForecast = {
            ...forecast,
            forecast: {
              forecastday: forecastDaysForTrip.slice(0, maxDaysToShow),
            },
          }

          const formattedData = formatWeatherData(filteredForecast)
          setWeatherData(formattedData)
        }
      } catch (err) {
        console.error("Error loading weather data:", err)
        setError("Weather forecast is not available right now.")
      } finally {
        setLoading(false)
      }
    }

    loadWeatherData()
  }, [destination, startDate, endDate])

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Weather Forecast</CardTitle>
        <p className="text-xs text-muted-foreground">Powered by WeatherAPI</p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <InlineLoading message="Loading weather data..." />
        ) : error ? (
          <div className="text-center py-4 text-muted-foreground">{error}</div>
        ) : message ? (
          <div className="text-center py-4 text-muted-foreground">{message}</div>
        ) : weatherData.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">No weather data available</div>
        ) : (
          <div className="space-y-3">
            {weatherData.map((day, index) => (
              <div key={index} className="flex items-center justify-between p-2 rounded-md border">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <div className="text-lg">{getWeatherEmoji(day.conditionCode)}</div>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{day.date}</p>
                    <p className="text-xs text-muted-foreground">{day.condition}</p>
                  </div>
                </div>
                <p className="font-medium">{day.temperature}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
