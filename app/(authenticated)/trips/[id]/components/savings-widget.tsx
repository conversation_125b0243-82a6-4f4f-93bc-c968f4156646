"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { DollarSign, Plus, Lock, CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import {
  TripSavings,
  getUserTripSavings,
  createTripSavings,
  updateTripSavings,
  addSavingsTransaction,
} from "@/lib/firebase-service"
import { checkUserTripStatus } from "@/lib/firebase/user-trip-service"
import { Timestamp } from "firebase/firestore"

interface TripSavingsWidgetProps {
  tripId: string
  tripName: string
  totalCost: number
  startDate?: Date
}

export function TripSavingsWidget({
  tripId,
  tripName,
  totalCost,
  startDate,
}: TripSavingsWidgetProps) {
  const user = useUser()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [savingsGoal, setSavingsGoal] = useState<TripSavings | null>(null)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [setupDialogOpen, setSetupDialogOpen] = useState(false)
  const [amount, setAmount] = useState("")
  const [goalAmount, setGoalAmount] = useState("")
  const [isPrivate, setIsPrivate] = useState(false)
  const [savingsMethod, setSavingsMethod] = useState("manual")
  const [submitting, setSubmitting] = useState(false)
  const [targetDate, setTargetDate] = useState<Date | null>(startDate || null)

  // Calculate days left until trip
  const daysLeft = savingsGoal?.targetDate
    ? Math.max(
        0,
        Math.ceil(
          (savingsGoal.targetDate.toDate().getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        )
      )
    : 0

  // Calculate daily savings needed
  const dailySavingsNeeded =
    daysLeft > 0 && savingsGoal
      ? ((savingsGoal.goalAmount - savingsGoal.savedAmount) / daysLeft).toFixed(2)
      : "0.00"

  // Initialize goal amount with the total cost when the component loads
  useEffect(() => {
    if (totalCost > 0) {
      setGoalAmount((totalCost / 2).toFixed(2))
    }
  }, [totalCost])

  useEffect(() => {
    const fetchSavingsGoal = async () => {
      if (!user) return

      try {
        setLoading(true)

        // First check if user is attending the trip
        const tripStatus = await checkUserTripStatus(user.uid, tripId)

        if (!tripStatus || tripStatus.status !== "going") {
          setLoading(false)
          return
        }

        const savings = await getUserTripSavings(user.uid, tripId)

        if (savings) {
          setSavingsGoal(savings)
        }
      } catch (error) {
        console.error("Error fetching savings goal:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchSavingsGoal()
  }, [user, tripId])

  const handleSetupSavings = async () => {
    if (!user) return

    try {
      setSubmitting(true)

      const goal = Number(goalAmount)

      if (isNaN(goal) || goal <= 0) {
        toast({
          title: "Invalid amount",
          description: "Please enter a valid savings goal amount",
          variant: "destructive",
        })
        return
      }

      // Use the selected target date or default to trip start date if available
      // If neither is available, default to 7 days before the current date + 60 days
      let savingsTargetDate: Date

      if (targetDate) {
        savingsTargetDate = targetDate
      } else if (startDate) {
        // Use trip start date
        savingsTargetDate = new Date(startDate)
      } else {
        // Fallback to 60 days from now
        savingsTargetDate = new Date()
        savingsTargetDate.setDate(savingsTargetDate.getDate() + 60)
      }

      const newSavings = {
        userId: user.uid,
        tripId,
        name: `${tripName} Savings`,
        goalAmount: goal,
        savedAmount: 0,
        isPrivate,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        targetDate: Timestamp.fromDate(savingsTargetDate),
      }

      const savingsId = await createTripSavings(newSavings)

      setSavingsGoal({
        ...newSavings,
        id: savingsId,
      } as TripSavings)
      setSetupDialogOpen(false)

      toast({
        title: "Savings goal created",
        description: "Your savings goal has been set up successfully",
      })
    } catch (error) {
      console.error("Error setting up savings:", error)
      toast({
        title: "Error",
        description: "Failed to set up savings goal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleAddSavings = async () => {
    if (!user || !savingsGoal) return

    try {
      setSubmitting(true)

      const amountToAdd = Number(amount)

      if (isNaN(amountToAdd) || amountToAdd <= 0) {
        toast({
          title: "Invalid amount",
          description: "Please enter a valid amount to add",
          variant: "destructive",
        })
        return
      }

      // Add transaction
      await addSavingsTransaction(user.uid, tripId, {
        amount: amountToAdd,
        date: Timestamp.now(),
        description: "Manual savings deposit",
      })

      // Update local state
      const updatedSavings = {
        ...savingsGoal,
        savedAmount: (savingsGoal?.savedAmount || 0) + amountToAdd,
        updatedAt: Timestamp.now(),
      }

      setSavingsGoal(updatedSavings)
      setAddDialogOpen(false)
      setAmount("")

      toast({
        title: "Savings added",
        description: `$${amountToAdd.toFixed(2)} has been added to your savings`,
      })
    } catch (error) {
      console.error("Error adding savings:", error)
      toast({
        title: "Error",
        description: "Failed to add savings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleTogglePrivacy = async () => {
    if (!user || !savingsGoal) return

    try {
      const updatedPrivacy = !savingsGoal.isPrivate

      await updateTripSavings(user.uid, tripId, {
        isPrivate: updatedPrivacy,
      })

      setSavingsGoal({
        ...savingsGoal,
        isPrivate: updatedPrivacy,
      })

      toast({
        title: "Privacy updated",
        description: updatedPrivacy
          ? "Your savings are now private"
          : "Your savings are now visible to the squad",
      })
    } catch (error) {
      console.error("Error updating privacy:", error)
      toast({
        title: "Error",
        description: "Failed to update privacy settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trip Savings</CardTitle>
          <CardDescription>Loading your savings data...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    )
  }

  if (!savingsGoal) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trip Savings</CardTitle>
          <CardDescription>Track your savings for this trip</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <DollarSign className="h-8 w-8 text-primary" />
          </div>
          <h3 className="font-medium text-lg mb-2">Set up your savings goal</h3>
          <p className="text-muted-foreground mb-6 max-w-md">
            Track your progress towards saving for this trip by setting a personal savings goal.
          </p>
          <Dialog open={setupDialogOpen} onOpenChange={setSetupDialogOpen}>
            <DialogTrigger asChild>
              <Button>Set Up Savings Goal</Button>
            </DialogTrigger>
            <DialogContent className="w-[95vw] max-w-[425px] p-4 sm:p-6">
              <DialogHeader className="text-center sm:text-left">
                <DialogTitle>Set Up Savings Goal</DialogTitle>
                <DialogDescription>
                  Create a savings goal for your trip to {tripName}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="goal-amount">Goal Amount ($)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="goal-amount"
                      type="number"
                      placeholder="0.00"
                      className="pl-9"
                      value={goalAmount}
                      onChange={(e) => {
                        // Only allow numeric input
                        const value = e.target.value.replace(/[^0-9.]/g, "")
                        setGoalAmount(value)
                      }}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Recommended: ${(totalCost / 2).toFixed(2)} (50% of your estimated cost)
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    This is your personal portion of the trip cost.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="target-date">Target Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="target-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !targetDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {targetDate ? format(targetDate, "PPP") : <span>Pick a target date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={targetDate || undefined}
                        onSelect={(date) => (date ? setTargetDate(date) : setTargetDate(null))}
                        initialFocus
                        disabled={(date) => date < new Date()}
                      />
                    </PopoverContent>
                  </Popover>
                  <p className="text-xs text-muted-foreground">
                    {startDate
                      ? `Recommended: Save by trip start (${format(startDate, "PPP")})`
                      : "Set a date to reach your savings goal"}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="savings-method">Savings Method</Label>
                  <Select value={savingsMethod} onValueChange={setSavingsMethod}>
                    <SelectTrigger id="savings-method">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manual">Manual Tracking</SelectItem>
                      <SelectItem value="automatic" disabled>
                        Automatic Savings (Coming Soon)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="private" checked={isPrivate} onCheckedChange={setIsPrivate} />
                  <Label htmlFor="private">Keep my savings private</Label>
                </div>
              </div>
              <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                <Button variant="outline" onClick={() => setSetupDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSetupSavings} disabled={submitting}>
                  {submitting ? "Setting Up..." : "Set Up Goal"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    )
  }

  const progress =
    savingsGoal?.savedAmount && savingsGoal?.goalAmount
      ? Math.min(100, Math.round((savingsGoal.savedAmount / savingsGoal.goalAmount) * 100))
      : 0

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Your Trip Savings</CardTitle>
            <CardDescription>Track your progress towards your goal</CardDescription>
          </div>
          {savingsGoal.isPrivate && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Lock className="h-3 w-3" /> Private
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h4 className="text-sm font-medium">Savings Progress</h4>
            <span className="text-sm font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>${savingsGoal?.savedAmount ? savingsGoal.savedAmount.toFixed(2) : "0.00"}</span>
            <span>${savingsGoal?.goalAmount ? savingsGoal.goalAmount.toFixed(2) : "0.00"}</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 sm:gap-4">
          <div className="p-2 sm:p-3 border rounded-md">
            <div className="text-sm text-muted-foreground mb-1">Days Left</div>
            <div className="text-xl sm:text-2xl font-bold">{daysLeft}</div>
          </div>
          <div className="p-2 sm:p-3 border rounded-md">
            <div className="text-sm text-muted-foreground mb-1">Daily Goal</div>
            <div className="text-xl sm:text-2xl font-bold">${dailySavingsNeeded}</div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
          <div className="flex items-center gap-2">
            <Switch
              checked={!savingsGoal.isPrivate}
              onCheckedChange={handleTogglePrivacy}
              id="privacy-toggle"
            />
            <Label htmlFor="privacy-toggle" className="text-sm cursor-pointer">
              Share with squad
            </Label>
          </div>
          <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-1" /> Add Savings
              </Button>
            </DialogTrigger>
            <DialogContent className="w-[95vw] max-w-[425px] p-4 sm:p-6">
              <DialogHeader className="text-center sm:text-left">
                <DialogTitle>Add to Your Savings</DialogTitle>
                <DialogDescription>
                  Record money you've set aside for your trip to {tripName}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Amount to Add ($)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="amount"
                      type="number"
                      placeholder="0.00"
                      className="pl-9"
                      value={amount}
                      onChange={(e) => {
                        // Only allow numeric input
                        const value = e.target.value.replace(/[^0-9.]/g, "")
                        setAmount(value)
                      }}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddSavings} disabled={submitting}>
                  {submitting ? "Adding..." : "Add Savings"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  )
}
