"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Users, Lock } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useToast } from "@/components/ui/use-toast"
import { InlineLoading } from "@/components/inline-loading"
// import { useRouter } from "next/navigation"
import { getSquadTripSavings, getUsersFromIds, getTrip } from "@/lib/firebase-service"
import { checkUserTripStatus } from "@/lib/firebase/user-trip-service"

interface SquadSavingsReadinessWidgetProps {
  tripId: string
  tripName?: string
  totalCost?: number
  isOrganizer?: boolean
}

interface MemberSavings {
  userId: string
  name: string
  savedAmount: number
  goalAmount: number
  isPrivate: boolean
  photoURL?: string
}

export function SquadSavingsReadinessWidget({
  tripId,
  isOrganizer = false,
}: SquadSavingsReadinessWidgetProps) {
  const user = useUser()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [memberSavings, setMemberSavings] = useState<MemberSavings[]>([])
  const [totalSaved, setTotalSaved] = useState(0)
  const [totalGoal, setTotalGoal] = useState(0)
  const [membersReady, setMembersReady] = useState(0)
  const [totalMembers, setTotalMembers] = useState(0)
  const [readiness, setReadiness] = useState(0)
  const [userCanView, setUserCanView] = useState(false)

  useEffect(() => {
    const fetchSquadSavings = async () => {
      if (!user) return

      try {
        setLoading(true)

        // Check if user is attending the trip
        const tripStatus = await checkUserTripStatus(user.uid, tripId)
        if (!tripStatus || tripStatus.status !== "going") {
          setLoading(false)
          return
        }

        // Get trip data to find squad members
        const trip = await getTrip(tripId)
        if (!trip || !trip.attendees || trip.attendees.length === 0) {
          setLoading(false)
          return
        }

        // Set user can view based on organizer status
        setUserCanView(isOrganizer || user.uid === trip.leaderId)

        // Get all squad members' savings
        const savingsData = await getSquadTripSavings(tripId)

        // Get user details for each member
        const userIds = savingsData.map((s) => s.userId)
        const users = await getUsersFromIds(userIds)

        // Map savings data with user details
        const mappedSavings: MemberSavings[] = savingsData.map((saving) => {
          const userDetails = users.find((u) => u.uid === saving.userId)
          return {
            userId: saving.userId,
            name: userDetails?.displayName || "Unknown User",
            savedAmount: saving.savedAmount,
            goalAmount: saving.goalAmount,
            isPrivate: saving.isPrivate,
            photoURL: userDetails?.photoURL,
          }
        })

        // Calculate totals
        const totalSavedAmount = mappedSavings.reduce((sum, member) => sum + member.savedAmount, 0)
        const totalGoalAmount = mappedSavings.reduce((sum, member) => sum + member.goalAmount, 0)
        const readyMembers = mappedSavings.filter(
          (member) => member.savedAmount >= member.goalAmount
        ).length

        setMemberSavings(mappedSavings)
        setTotalSaved(totalSavedAmount)
        setTotalGoal(totalGoalAmount)
        setMembersReady(readyMembers)
        setTotalMembers(trip.attendees.length)
        setReadiness(
          totalGoalAmount > 0
            ? Math.min(100, Math.round((totalSavedAmount / totalGoalAmount) * 100))
            : 0
        )
      } catch (error) {
        console.error("Error fetching squad savings:", error)
        toast({
          title: "Error",
          description: "Failed to load squad savings data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchSquadSavings()
  }, [user, tripId, toast, isOrganizer])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Squad Savings</CardTitle>
          <CardDescription>Loading squad savings data...</CardDescription>
        </CardHeader>
        <CardContent>
          <InlineLoading size="large" />
        </CardContent>
      </Card>
    )
  }

  if (memberSavings.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Squad Savings</CardTitle>
          <CardDescription>No savings data available yet</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <p className="text-muted-foreground mb-4">
            No squad members have set up savings goals for this trip yet
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Squad Savings</CardTitle>
            <CardDescription>Track how ready your squad is for this trip</CardDescription>
          </div>
          <Badge variant={readiness >= 75 ? "outline" : "secondary"}>
            {readiness >= 75 ? "Ready" : "In Progress"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">Total Saved</p>
            <p className="text-xl font-bold">${totalSaved.toLocaleString()}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">Combined Goal</p>
            <p className="text-xl font-bold">${totalGoal.toLocaleString()}</p>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span>{readiness}% of goal</span>
            <span>
              {membersReady} of {totalMembers} members ready
            </span>
          </div>
          <Progress value={readiness} className="h-2" />
        </div>

        {userCanView ? (
          <div className="space-y-3 pt-2">
            <h4 className="text-sm font-medium">Member Savings</h4>
            {memberSavings.map((member) => (
              <div
                key={member.userId}
                className="flex items-center justify-between p-2 rounded-md border"
              >
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden">
                    {member.photoURL ? (
                      <img
                        src={member.photoURL}
                        alt={member.name}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <Users className="h-4 w-4" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{member.name}</p>
                    <div className="flex items-center gap-1">
                      <Progress
                        value={
                          member.goalAmount > 0
                            ? Math.min(
                                100,
                                Math.round((member.savedAmount / member.goalAmount) * 100)
                              )
                            : 0
                        }
                        className="h-1 w-20"
                      />
                      <span className="text-xs text-muted-foreground">
                        {member.goalAmount > 0
                          ? Math.min(
                              100,
                              Math.round((member.savedAmount / member.goalAmount) * 100)
                            )
                          : 0}
                        %
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {member.isPrivate ? (
                    <div className="flex items-center text-muted-foreground">
                      <Lock className="h-3 w-3 mr-1" />
                      <span className="text-sm">Private</span>
                    </div>
                  ) : (
                    <p className="text-sm font-medium">
                      ${member.savedAmount.toLocaleString()} / ${member.goalAmount.toLocaleString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="pt-4 text-center">
            <p className="text-sm text-muted-foreground">
              Individual member savings details are only visible to trip organizers
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              View Details
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Squad Savings Details</DialogTitle>
              <DialogDescription>Detailed view of your squad's savings progress</DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="text-center text-muted-foreground">
                Detailed squad savings view will be available in a future update.
              </p>
            </div>
            <DialogFooter>
              <Button
                onClick={() => {
                  toast({
                    title: "Coming Soon",
                    description: "We're working on this feature!",
                  })
                }}
              >
                OK
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}
