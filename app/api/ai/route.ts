import { type NextRequest, NextResponse } from "next/server"
import OpenA<PERSON> from "openai"
import type { Trip, User } from "@/lib/firebase-service"
import { verifyAuth } from "@/lib/api-auth"
import { ALLOWED_TRAVEL_TYPES } from "@/lib/constants/travel-types"
import {
  findAffiliateLink,
  AFFILIATE_LINK_TAGS,
  affiliateLinksMap,
} from "@/lib/affiliate-links-map"

// Create a server-side OpenAI API client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Helper function to format dates that can handle both Firestore Timestamps and regular Date objects/strings
function formatDate(dateValue: any): string {
  if (!dateValue) return "Not set"

  try {
    // Check if it's a Firestore Timestamp with toDate method
    if (
      dateValue &&
      typeof dateValue === "object" &&
      "toDate" in dateValue &&
      typeof dateValue.toDate === "function"
    ) {
      return new Date(dateValue.toDate()).toLocaleDateString()
    }

    // Handle regular Date objects or strings
    return new Date(dateValue).toLocaleDateString()
  } catch (error) {
    console.error("Error formatting date:", error)
    return "Invalid date"
  }
}

// Define types for our suggestion systems
interface TaskSuggestion {
  title: string
  description: string
  category: "planning" | "booking" | "preparation" | "coordination" | "during-trip"
  priority: "high" | "medium" | "low"
  tags?: string[]
  hasAffiliateLink?: boolean
  affiliateLink?: AffiliateLink
}

interface TripSuggestion {
  destination: string
  description: string
  tags: string[]
  budget: string
  imageUrl?: string
  placeId?: string
}

interface ActivitySuggestion {
  title: string
  description: string
  cost: string
  duration: string
}

interface ItinerarySuggestion {
  title: string
  description: string
  day: number
  timeOfDay: "morning" | "afternoon" | "evening"
  duration: string
}

interface AffiliateLink {
  url: string
  title: string
  description: string
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    const { type, data } = await request.json()

    // Validate required parameters
    if (!type) {
      return NextResponse.json({ error: "Missing required parameter: type" }, { status: 400 })
    }

    if (!data) {
      return NextResponse.json({ error: "Missing required parameter: data" }, { status: 400 })
    }

    // Log the AI request to Firestore for auditing
    // Temporarily disable logging to Firestore until security rules are updated
    /*
    try {
      // Create a log object with required fields
      const logData: {
        type: string
        data: any
        timestamp: any
        userId?: string
      } = {
        type,
        data,
        timestamp: serverTimestamp(),
      }

      // Only add userId if it's provided
      if (userId) {
        logData.userId = userId
      }

      await addDoc(collection(db, "ai_requests"), logData)
    } catch (logError) {
      // Log the error but continue with the request
      console.error("Error logging AI request:", logError)
      // Don't fail the whole request if logging fails
    }
    */

    // Just log to console for now
    console.log(`AI request: ${type} from user ${userId || "unknown"}`)

    let result

    switch (type) {
      case "trip_suggestions":
        result = await generateTripSuggestions(data.preferences, data.previousSuggestions)
        break
      case "itinerary":
        result = await generateItinerary(data.tripDetails)
        break
      case "chat_message":
        result = await processAIChatMessage(data.message, data.chatHistory)
        break
      case "task_suggestions":
        result = await generateTaskSuggestions(
          data.trip,
          data.userPreferences,
          data.userLocation,
          data.existingTasks || []
        )
        break
      case "task_suggestions_with_links":
        result = await generateTaskSuggestionsWithLinks(
          data.trip,
          data.userPreferences,
          data.existingTasks,
          data.userLocation
        )
        break
      case "task_completion_suggestions":
        result = await generateTaskCompletionSuggestions(
          data.taskTitle,
          data.taskDescription,
          data.tripDestination
        )
        break
      case "destination_activities":
        result = await generateDestinationActivities(
          data.destination,
          data.budget,
          data.preferences
        )
        break
      case "activity_suggestions":
        result = await generateActivitySuggestions(data.trip, data.userPreferences, data.day)
        break
      case "affiliate_links":
        result = await suggestAffiliateLinks(data.taskTitle, data.taskDescription, data.destination)
        break
      default:
        return NextResponse.json({ error: "Invalid request type" }, { status: 400 })
    }

    return NextResponse.json({ result })
  } catch (error) {
    console.error("AI API error:", error)
    return NextResponse.json(
      { error: "An error occurred processing your request" },
      { status: 500 }
    )
  }
}

// Server-side implementation of OpenAI functions
async function generateTripSuggestions(preferences: any, previousSuggestions?: TripSuggestion[]) {
  try {
    // Extract key preferences
    const travelTypes = preferences.travelPreferences || []
    const budgetRange = preferences.budgetRange || [500, 2000]
    const travelSeasons = preferences.preferredTravelSeasons || []
    const availability = preferences.availabilityPreferences || []

    // Format budget for prompt
    const minBudget = Array.isArray(budgetRange) ? budgetRange[0] : 500
    const maxBudget = Array.isArray(budgetRange) ? budgetRange[1] : 2000
    const budgetStr = `$${minBudget} - $${maxBudget}`

    // Extract previous destinations to avoid repeating them
    const previousDestinations = previousSuggestions
      ? previousSuggestions.map((s) => s.destination).filter(Boolean)
      : []

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a travel planning assistant for BroTrip, an app that helps friends plan trips together. Provide helpful, concise trip suggestions based on the user's preferences. Focus on destinations all over the world that match their travel type preferences and budget range. Ensure variety in your suggestions and avoid repeating destinations that were previously suggested.",
        },
        {
          role: "user",
          content: `Generate 3 trip suggestions based on these preferences:
          Travel Types: ${travelTypes.join(", ") || "Any"}
          Budget Range: ${budgetStr}
          Preferred Seasons: ${travelSeasons.join(", ") || "Any"}
          Availability: ${availability.join(", ") || "Flexible"}
          ${previousDestinations.length > 0 ? `Previously suggested (avoid these): ${previousDestinations.join(", ")}` : ""}

          For each suggestion, include:
          1. Destination name
          2. Brief description (1-2 sentences)
          3. 2 relevant tags from this list only: ${ALLOWED_TRAVEL_TYPES.join(", ")}
          4. Estimated budget range per person

          Format as JSON with this structure:
          {
            "suggestions": [
              {
                "destination": "Destination Name",
                "description": "Brief description of the destination",
                "tags": ["Tag1", "Tag2"],
                "budget": "$X - $Y per person"
              },
              ...
            ]
          }`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        // Filter tags to ensure they only use allowed travel types
        const validatedSuggestions = parsedContent.suggestions.map(
          (suggestion: TripSuggestion) => ({
            ...suggestion,
            tags: suggestion.tags.filter((tag) => ALLOWED_TRAVEL_TYPES.includes(tag as any)),
          })
        )
        return validatedSuggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            destination: "Barcelona, Spain",
            description:
              "Enjoy stunning architecture, delicious cuisine, and beautiful beaches in this Mediterranean gem.",
            tags: ["City", "Culture"],
            budget: budgetStr + " per person",
          },
          {
            destination: "Tokyo, Japan",
            description:
              "Experience the perfect blend of traditional culture and futuristic technology in this vibrant metropolis.",
            tags: ["City"],
            budget: budgetStr + " per person",
          },
          {
            destination: "Costa Rica",
            description:
              "Discover lush rainforests, stunning beaches, and abundant wildlife in this Central American paradise.",
            tags: ["Adventure"],
            budget: budgetStr + " per person",
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return the raw content if parsing fails
      return content
    }
  } catch (error) {
    console.error("Error generating trip suggestions:", error)
    throw error
  }
}

async function generateItinerary(tripDetails: any) {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a travel planning assistant for BroTrip. Create detailed day-by-day itineraries for trips.",
        },
        {
          role: "user",
          content: `Create a detailed itinerary for this trip: ${JSON.stringify(tripDetails)}`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
    })

    return completion.choices[0].message.content
  } catch (error) {
    console.error("Error generating itinerary:", error)
    throw error
  }
}

async function processAIChatMessage(message: string, chatHistory: any[]) {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a helpful travel assistant for BroTrip, an app that helps friends plan trips together. Provide concise, helpful responses to user queries about trip planning, activities, accommodations, and travel tips.",
        },
        ...chatHistory.map((msg: any) => ({
          role: msg.sender.id === "ai-assistant" ? ("assistant" as const) : ("user" as const),
          content: msg.content,
        })),
        {
          role: "user" as const,
          content: message,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
    })

    return completion.choices[0].message.content
  } catch (error) {
    console.error("Error processing AI chat message:", error)
    throw error
  }
}

async function generateTaskSuggestions(
  trip: Trip,
  userPreferences: Partial<User>,
  userLocation?: string,
  existingTasks: string[] = []
): Promise<TaskSuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for BroTrip, an app that helps friends plan trips together.
          Generate a list of initial tasks that users should complete when planning a trip.
          Format your response as a JSON object with a 'suggestions' array containing TaskSuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "category": "planning" | "booking" | "preparation" | "coordination" | "during-trip",
                "priority": "high" | "medium" | "low",
                "tags": ["tag1", "tag2"] // 2-3 tags from the list below
              },
              ...
            ]
          }
          Focus on practical, actionable tasks that will help the user prepare for their trip.

          IMPORTANT:
          1. For each task, include 2-3 tags from this list only: ${AFFILIATE_LINK_TAGS.join(", ")}
          2. These tags will be used to match tasks with affiliate links, so choose them carefully.
          3. Ensure each suggestion is unique and not similar to any existing tasks.
          4. Avoid suggesting tasks that are variations of the same activity or responsibility.`,
        },
        {
          role: "user" as const,
          content: `Generate EXACTLY 6 task suggestions for a trip to ${trip.destination}.
          Trip dates: ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          User preferences: ${JSON.stringify(userPreferences)}.
          ${userLocation ? `User location: ${userLocation}` : ""}
          ${existingTasks.length > 0 ? `IMPORTANT - Existing tasks (DO NOT suggest similar tasks): ${existingTasks.join(", ")}` : ""}

          Include a mix of planning, booking, preparation, and coordination tasks.
          Make sure to include appropriate tags for each task from the provided list.
          Ensure each suggestion is COMPLETELY DIFFERENT from any existing tasks.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        return parsedContent.suggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: "Book flights",
            description: `Look for flights to ${trip.destination}`,
            category: "booking" as const,
            priority: "high" as const,
            tags: ["flight", "air travel", "booking"],
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: "Book flights",
          description: `Look for flights to ${trip.destination}`,
          category: "booking" as const,
          priority: "high" as const,
          tags: ["flight", "air travel", "booking"],
        },
      ]
    }
  } catch (error) {
    console.error("Error generating task suggestions:", error)
    throw error
  }
}

async function generateTaskCompletionSuggestions(
  taskTitle: string,
  taskDescription: string,
  tripDestination: string
): Promise<{ suggestion: string; affiliateLink: AffiliateLink | null }[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for BroTrip, an app that helps friends plan trips together.
          Generate practical suggestions for how to complete a specific travel-related task.
          Format your response as a JSON object with a 'suggestions' array containing objects with the following structure:
          {
            "suggestions": [
              {
                "suggestion": string // A practical, actionable suggestion for completing the task
              },
              ...
            ]
          }
          Provide 2 specific, helpful suggestions that would help someone complete this task.
          Focus on practical advice that includes specific websites, tools, or approaches.`,
        },
        {
          role: "user" as const,
          content: `Generate suggestions for completing this task for a trip to ${tripDestination}:\n\nTask: ${taskTitle}\n${taskDescription ? `Description: ${taskDescription}` : ""}`,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        // Process each suggestion to add affiliate links
        const suggestionsWithLinks = await Promise.all(
          parsedContent.suggestions.map(async (item: { suggestion: string }) => {
            const affiliateLink = await suggestAffiliateLinks(
              taskTitle,
              item.suggestion,
              tripDestination
            )
            return {
              suggestion: item.suggestion,
              affiliateLink,
            }
          })
        )
        return suggestionsWithLinks
      } else {
        // If the structure is unexpected but valid JSON, create a default structure
        console.log("Unexpected response structure from OpenAI:", parsedContent)
        const defaultSuggestion = `Research options for ${taskTitle} in ${tripDestination} online`
        const affiliateLink = await suggestAffiliateLinks(
          taskTitle,
          defaultSuggestion,
          tripDestination
        )
        return [
          {
            suggestion: defaultSuggestion,
            affiliateLink,
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      const defaultSuggestion = `Research options for ${taskTitle} in ${tripDestination} online`
      return [
        {
          suggestion: defaultSuggestion,
          affiliateLink: null,
        },
      ]
    }
  } catch (error) {
    console.error("Error generating task completion suggestions:", error)
    throw error
  }
}

// Optimized function that combines task suggestions and affiliate links in one request
async function generateTaskSuggestionsWithLinks(
  trip: Trip,
  _userPreferences: Partial<User>, // Prefixed with underscore to indicate it's not used
  existingTasks: string[] = [],
  userLocation?: string
): Promise<{
  suggestions: TaskSuggestion[]
  affiliateLinks: Record<string, AffiliateLink | null>
}> {
  try {
    // Use a more concise prompt to reduce token usage and improve response time
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini", // Using environment variable with default
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for BroTrip. Generate 3-5 practical tasks for trip planning.
          Format as JSON:
          {
            "suggestions": [
              {
                "title": "Task title",
                "description": "Brief description",
                "category": "planning" | "booking" | "preparation" | "coordination" | "during-trip",
                "priority": "high" | "medium" | "low",
                "tags": ["tag1", "tag2"] // 2 tags from: ${AFFILIATE_LINK_TAGS.slice(0, 10).join(", ")}...
              }
            ]
          }
          Be concise. Focus on essential tasks only.

          IMPORTANT:
          1. Only use tags from this list: ${AFFILIATE_LINK_TAGS.join(", ")}
          2. Ensure each suggestion is unique and not similar to any existing tasks.
          3. Avoid suggesting tasks that are variations of the same activity or responsibility.
          4. Each task should be completely different from existing tasks in both purpose and content.`,
        },
        {
          role: "user" as const,
          content: `Trip to ${trip.destination} from ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          ${existingTasks.length > 0 ? `IMPORTANT - Existing tasks (DO NOT suggest similar tasks): ${existingTasks.join(", ")}` : ""}
          ${userLocation ? `From: ${userLocation}` : ""}
          Include mix of planning, booking, and preparation tasks.
          Ensure each suggestion is COMPLETELY DIFFERENT from any existing tasks.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 800, // Reduced token limit for faster response
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content

    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        const suggestions = parsedContent.suggestions
        console.log("Parsed suggestions:", suggestions)
        // Generate affiliate links for all suggestions in one batch
        const affiliateLinks: Record<string, AffiliateLink | null> = {}

        for (const suggestion of suggestions) {
          // Find affiliate link for this suggestion using the centralized mapper
          // If the suggestion has tags, we'll use those directly
          if (suggestion.tags && suggestion.tags.length > 0) {
            // Find the best matching affiliate link based on the tags
            const matchingLinks = affiliateLinksMap.filter((link) => {
              return suggestion.tags.some((tag: string) => link.tags.includes(tag))
            })

            if (matchingLinks.length > 0) {
              // Sort by number of matching tags and take the best match
              const bestMatch = matchingLinks.sort((a, b) => {
                const aMatches = a.tags.filter((tag) => suggestion.tags.includes(tag)).length
                const bMatches = b.tags.filter((tag) => suggestion.tags.includes(tag)).length
                return bMatches - aMatches
              })[0]

              affiliateLinks[suggestion.title] = bestMatch
            } else {
              // Fallback to text-based matching if no tag matches
              affiliateLinks[suggestion.title] = findAffiliateLink(
                suggestion.title,
                suggestion.description,
                trip.destination
              )
            }
          } else {
            // If no tags, use the text-based matching
            affiliateLinks[suggestion.title] = findAffiliateLink(
              suggestion.title,
              suggestion.description,
              trip.destination
            )
          }
        }

        return {
          suggestions,
          affiliateLinks,
        }
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return {
          suggestions: [
            {
              title: "Book flights",
              description: `Look for flights to ${trip.destination}`,
              category: "booking" as const,
              priority: "high" as const,
              tags: ["flight", "air travel", "booking"],
            },
          ],
          affiliateLinks: {},
        }
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return {
        suggestions: [
          {
            title: "Book flights",
            description: `Look for flights to ${trip.destination}`,
            category: "booking" as const,
            priority: "high" as const,
            tags: ["flight", "air travel", "booking"],
          },
        ],
        affiliateLinks: {},
      }
    }
  } catch (error) {
    console.error("Error generating task suggestions with links:", error)
    throw error
  }
}

async function suggestAffiliateLinks(
  taskTitle: string,
  taskDescription: string,
  destination: string
): Promise<AffiliateLink | null> {
  // Use the centralized affiliate link mapper
  return findAffiliateLink(taskTitle, taskDescription, destination)
}

async function generateDestinationActivities(
  destination: string,
  budget?: string,
  preferences?: string[]
): Promise<ActivitySuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for BroTrip, an app that helps friends plan trips together.
          Generate a list of activity suggestions for a destination.
          Format your response as a JSON object with a 'suggestions' array containing ActivitySuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "cost": string,
                "duration": string
              },
              ...
            ]
          }
          Focus on popular and interesting activities that visitors would enjoy.`,
        },
        {
          role: "user" as const,
          content: `Generate 3 activity suggestions for ${destination}.
          ${budget ? `Budget range: ${budget}` : ""}
          ${preferences && preferences.length > 0 ? `Travel preferences: ${preferences.join(", ")}` : ""}

          Include a mix of popular attractions and local experiences. For each activity, provide:
          1. A title
          2. A brief description
          3. Estimated cost (per person)
          4. Typical duration`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        return parsedContent.suggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: `Visit popular attractions in ${destination}`,
            description: `Explore the most famous sights in ${destination}`,
            cost: budget || "$30 - $50",
            duration: "3-4 hours",
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: `Visit popular attractions in ${destination}`,
          description: `Explore the most famous sights in ${destination}`,
          cost: budget || "$30 - $50",
          duration: "3-4 hours",
        },
      ]
    }
  } catch (error) {
    console.error("Error generating destination activities:", error)
    throw error
  }
}

async function generateActivitySuggestions(
  trip: Trip,
  userPreferences: Partial<User>,
  day?: number
): Promise<ItinerarySuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for BroTrip, an app that helps friends plan trips together.
          Generate a list of activity suggestions for a trip itinerary.
          Format your response as a JSON object with a 'suggestions' array containing ItinerarySuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "day": number,
                "timeOfDay": "morning" | "afternoon" | "evening",
                "duration": string
              },
              ...
            ]
          }
          Focus on activities that match the user's preferences and the destination.`,
        },
        {
          role: "user" as const,
          content: `Generate ${day ? "3-4 activity suggestions for day " + day : "6-8 activity suggestions"} of a trip to ${trip.destination}.
          Trip dates: ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          User preferences: ${JSON.stringify(userPreferences)}.
          Include a mix of popular attractions, local experiences, and activities that match the user's travel preferences.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        return parsedContent.suggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: "Explore local attractions",
            description: `Visit popular attractions in ${trip.destination}`,
            day: day || 1,
            timeOfDay: "morning" as const,
            duration: "3 hours",
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: "Explore local attractions",
          description: `Visit popular attractions in ${trip.destination}`,
          day: day || 1,
          timeOfDay: "morning" as const,
          duration: "3 hours",
        },
      ]
    }
  } catch (error) {
    console.error("Error generating activity suggestions:", error)
    throw error
  }
}
