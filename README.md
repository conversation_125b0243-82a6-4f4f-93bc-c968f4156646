# BroTrips.ai

BroTrips.ai is a modern trip planning application designed to help friends organize and coordinate their travel plans seamlessly.

## Overview

BroTrips.ai simplifies group travel planning by providing a platform where users can create squads, plan trips, and collaborate on travel details. The application leverages modern web technologies to deliver a responsive and intuitive user experience.

## Features

### Core Features

- **Squad Management**: Create and manage groups of friends (squads) for different travel circles
- **Trip Planning**: Plan trips with specific destinations, dates, and budgets
- **Collaborative Tools**: Work together with your squad to organize travel details
- **Progressive Web App**: Works offline and can be installed on mobile devices

### Module Features

#### Trip Management

- **Trip Overview**: View trip details, progress, and weather forecasts
- **Trip Tasks**: Assign and track tasks with AI-powered suggestions
- **Trip Itinerary**: Plan daily activities with AI-generated recommendations
- **Trip Savings**: Track individual and group savings goals
- **Trip Attendance**: Manage attendance status for squad members

#### Squad Management

- **Squad Overview**: View squad details and members
- **Trip Ideas**: Get AI-powered trip suggestions based on preferences
- **Trip Voting**: Vote on trip suggestions with discussion features
- **Squad Invitations**: Invite friends to join your squads

#### AI Integration

- **Trip Suggestions**: AI-generated trip ideas based on user preferences
- **Task Suggestions**: AI-powered task recommendations for trip planning
- **Activity Suggestions**: AI-generated activities for trip itineraries
- **Affiliate Links**: Smart affiliate link integration for booking and planning

#### User Experience

- **Responsive Design**: Optimized for both desktop and mobile devices
- **Dark Mode**: Toggle between light and dark themes
- **Loading States**: Consistent loading indicators across the application
- **User Settings**: Personalized preferences for travel and app experience

## Technology Stack

### Frontend

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript, React
- **State Management**: Zustand for global state management
- **UI Components**: Custom UI components built with Tailwind CSS and shadcn/ui
- **Styling**: Tailwind CSS with CSS variables for theming

### Backend & Services

- **Authentication**: Firebase Authentication with Next.js App Router route groups
- **Database**: Firestore for real-time data
- **Storage**: Firebase Storage for user uploads
- **API Integration**: OpenAI API for AI features
- **Location Services**: Google Places API for location data and images
- **Weather Data**: Weather API integration

### DevOps & Tooling

- **Package Manager**: pnpm for efficient dependency management
- **Code Quality**: ESLint, Prettier, TypeScript strict mode
- **Git Hooks**: Husky for pre-commit and pre-push checks
- **Deployment**: Vercel for hosting and serverless functions

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- pnpm

### Installation

1. Clone the repository:

```bash
git clone https://github.com/jmalcolm27/BroTrips.ai.git
```

2. Navigate to the project directory:

```bash
cd BroTrips.ai
```

3. Install dependencies:

```bash
pnpm install
```

This will automatically set up Git hooks for code quality checks through Husky.

### Development Workflow

The project uses several tools to ensure code quality:

- **ESLint**: Checks for code quality issues
- **Prettier**: Formats code consistently
- **TypeScript**: Ensures type safety
- **Husky**: Runs checks before commits and pushes
- **lint-staged**: Runs linters on staged files
- **commitlint**: Enforces conventional commit messages

These tools run automatically when you commit or push code:

- **Pre-commit**: Runs ESLint, Prettier, and TypeScript type checking on staged files
- **Pre-push**: Runs type checking and build verification

If you want to run them manually:

```bash
# Run ESLint
pnpm lint

# Fix ESLint issues
pnpm lint:fix

# Format code with Prettier
pnpm format

# Check TypeScript types
pnpm check-types

# Run lint-staged manually
pnpm lint-staged
```

4. Set up environment variables:
   Copy the `.env.example` file to `.env.local` and fill in the required values:

```bash
cp .env.example .env.local
```

Required environment variables include:

- OpenAI API key
- Firebase configuration
- Stripe API keys and configuration

5. Start the development server:

```bash
pnpm dev
```

## Project Structure

### Overview

- `/app`: Next.js application routes and pages
- `/components`: Reusable UI components
- `/hooks`: Custom React hooks
- `/lib`: Utility functions and API clients

### App Module

The app directory contains all the Next.js routes and pages, organized by feature:

```
/app
├── (authenticated)    # Protected routes requiring authentication
│   ├── admin          # Admin panel pages
│   │   ├── dashboard  # Admin dashboard
│   │   ├── settings   # Admin settings
│   │   ├── suggestions # AI suggestion management
│   │   ├── trips      # Trip management
│   │   └── users      # User management
│   ├── calendar       # Calendar view
│   ├── dashboard      # User dashboard
│   ├── settings       # User settings
│   │   └── components # Settings components
│   ├── squads         # Squad management
│   │   ├── [id]       # Individual squad
│   │   │   ├── components # Squad components
│   │   │   └── vote   # Trip voting
│   │   └── create     # Create squad
│   └── trips          # Trip management
│       ├── [id]       # Individual trip
│       │   ├── components # Trip components
│       │   │   ├── itinerary # Itinerary components
│       │   │   └── tasks # Task components
│       │   └── tasks  # Task management
│       ├── create     # Create trip
│       │   └── components # Trip creation components
│       └── plan       # Trip planning
├── actions            # Server actions
├── api                # API routes
│   ├── ai             # OpenAI integration
│   ├── auth           # Authentication endpoints
│   ├── images         # Image handling
│   │   ├── google-places # Google Places API integration
│   │   └── unsplash   # Unsplash API integration
│   ├── places         # Places API
│   │   ├── autocomplete # Location autocomplete
│   │   └── search     # Location search
│   └── weather        # Weather API integration
├── invitations        # Squad invitations
├── login              # User login
├── savings            # Savings management
│   ├── [id]           # Individual savings
│   └── create         # Create savings
└── signup             # User signup
```

### Components Module

The components directory contains reusable UI components:

```
/components
├── admin              # Admin-specific components
├── trip-voting        # Trip voting components
├── ui                 # UI component library
├── app-header.tsx     # Main application header
├── app-sidebar.tsx    # Main application sidebar
├── inline-loading.tsx # Loading indicator
├── optimized-image.tsx # Image optimization
├── page-loading.tsx   # Page loading component
├── providers.tsx      # Application providers
├── settle-up-modal.tsx # Expense settlement
├── store-initializer.tsx # Zustand store initialization
└── theme-provider.tsx  # Theme management with zustand integration
```

### Lib Module

The lib directory contains utility functions and service integrations:

```
/lib
├── firebase           # Firebase services by domain
│   ├── auth-service.ts     # Authentication services
│   ├── invitation-service.ts # Invitation management
│   ├── itinerary-service.ts  # Itinerary management
│   ├── squad-service.ts      # Squad management
│   ├── task-service.ts       # Task management
│   ├── trip-savings-service.ts # Trip savings
│   ├── trip-service.ts       # Trip management
│   ├── user-service.ts       # User management
│   └── user-trip-service.ts  # User-trip relationships
├── auth-context.tsx   # Authentication context
├── firebase.ts        # Firebase initialization
├── firebase-service.ts # Legacy Firebase service
├── google-places.ts   # Google Places API integration
├── openai.ts          # OpenAI API integration
├── store.ts           # Global state management
└── utils.ts           # Utility functions
```

### Hooks Module

The hooks directory contains custom React hooks:

```
/hooks
├── use-mobile.tsx     # Mobile detection
├── use-pwa-install.tsx # PWA installation
└── use-window-size.tsx # Window size detection
```

## Architecture and Module Purposes

### App Architecture

BroTrips.ai follows the Next.js App Router architecture with a clear separation between:

- **Server Components**: Handle data fetching and initial rendering
- **Client Components**: Handle interactivity and state management
- **API Routes**: Handle server-side operations and third-party integrations

### Authentication Architecture

The application uses a modern authentication approach leveraging Next.js App Router features:

- **Route Groups**: The `(authenticated)` folder contains all protected routes that require authentication
- **Layout-based Auth**: Each protected route group has a layout component that checks authentication status
- **API Protection**: Middleware intercepts requests to protected API routes and verifies Firebase authentication tokens
- **Firebase Admin SDK**: Server-side token verification for secure API access
- **Client-side Auth**: React Context provides authentication state to client components

This architecture provides several benefits:

- **Centralized Auth Logic**: Authentication checks are handled at the layout level, not in individual pages
- **Improved Security**: API routes are protected by middleware and Firebase Admin SDK verification
- **Better UX**: Loading states and redirects are handled consistently across the application

### Module Purposes

#### Trip Module

The Trip module handles all aspects of trip management, including:

- Trip creation and editing
- Task management with AI-powered suggestions
- Itinerary planning with daily activities
- Trip progress tracking
- Weather integration for trip dates
- Trip savings goals and tracking

#### Squad Module

The Squad module manages groups of friends who plan trips together:

- Squad creation and member management
- Trip idea generation with AI suggestions
- Trip voting and discussion
- Squad-based trip planning

#### Settings Module

The Settings module handles user preferences and account management:

- Profile settings and personal information
- Travel preferences for AI suggestions
- App preferences including theme settings
- Notification preferences
- Privacy and security settings

#### Admin Module

The Admin module provides administrative tools for managing the application:

- User management
- Trip oversight
- AI suggestion management
- Application settings

### Firebase Architecture

The Firebase integration is organized by domain with separate service files for each entity:

- **User Service**: User management and preferences
- **Squad Service**: Squad creation and member management
- **Trip Service**: Trip details and management
- **Task Service**: Task creation and tracking
- **Invitation Service**: Squad invitation handling
- **User-Trip Service**: Relationship between users and trips
- **Trip Savings Service**: Savings goals and tracking
- **Itinerary Service**: Trip itinerary management

#### Firebase Client SDK

The application uses the Firebase Client SDK for real-time data synchronization and authentication in client components:

- **Authentication**: Firebase Auth for user sign-in and session management
- **Firestore**: Real-time database for application data
- **Storage**: File storage for user uploads

#### Firebase Admin SDK

For secure server-side operations, the application uses the Firebase Admin SDK:

- **Token Verification**: Securely verify authentication tokens in API routes
- **Admin Operations**: Perform privileged operations that bypass security rules
- **Server-side Auth**: Handle authentication in server components and API routes

## Developer Tools

This project uses several developer tools to maintain code quality and consistency:

### Code Quality

- **ESLint**: JavaScript and TypeScript linting
- **Prettier**: Code formatting
- **TypeScript**: Static type checking with strict mode enabled

### Git Workflow

- **Husky**: Git hooks for pre-commit and commit-msg validation
- **lint-staged**: Run linters on staged files before committing
- **commitlint**: Enforce conventional commit message format

### VS Code Integration

Recommended extensions:

- ESLint
- Prettier
- TypeScript Error Translator
- Tailwind CSS IntelliSense

The project includes VS Code settings to automatically format code on save and apply ESLint fixes.

## Deploying Firestore Rules and Indexes

### Firestore Security Rules

The application uses Firestore security rules to protect data access. These rules are defined in the `firestore.rules` file at the root of the project.

To deploy updated security rules:

1. Make your changes to the `firestore.rules` file
2. Use the Firebase CLI to deploy the rules:

```bash
# Deploy Firestore security rules
firebase deploy --only firestore:rules
```

If you have multiple Firebase projects configured, specify the project:

```bash
# Deploy to a specific project
firebase deploy --only firestore:rules --project your-project-id
```

### Firestore Indexes

Firestore indexes are defined in the `firestore.indexes.json` file. These indexes are required for complex queries used in the application.

To deploy updated indexes:

```bash
# Deploy Firestore indexes
firebase deploy --only firestore:indexes
```

Or for a specific project:

```bash
# Deploy to a specific project
firebase deploy --only firestore:indexes --project your-project-id
```

### Deploying Both Rules and Indexes

To deploy both rules and indexes at once:

```bash
# Deploy both rules and indexes
firebase deploy --only firestore
```

### Checking Deployment Status

You can check the status of your Firestore rules and indexes in the Firebase Console:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database
4. Click on the "Rules" tab to view deployed rules
5. Click on the "Indexes" tab to view deployed indexes

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Thanks to all contributors who have helped shape BroTrips.ai
- Built with ❤️ for travelers and friends
