"use client"

import Link from "next/link"
import {
  Home,
  Users,
  Compass,
  Calendar,
  Settings,
  LogOut,
  PlusCircle,
  X,
  MessageSquare,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet"
import { useRouter } from "next/navigation"
import { logOut } from "@/lib/firebase-service"
import { toast } from "@/components/ui/use-toast"
import { useSidebar } from "@/hooks/use-sidebar"

// Navigation links component to avoid duplication
function NavigationLinks({ onClick }: { onClick?: () => void }) {
  const router = useRouter()

  const handleLogout = async () => {
    try {
      await logOut()
      router.push("/login")
      if (onClick) onClick()
    } catch (error) {
      console.error("Error logging out:", error)
      toast({
        title: "Error",
        description: "Failed to log out. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <nav className="space-y-1">
      <Link
        href="/dashboard"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <Home className="h-5 w-5 text-primary" />
        <span>Dashboard</span>
      </Link>
      <Link
        href="/squads"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <Users className="h-5 w-5 text-primary" />
        <span>My Squads</span>
      </Link>
      <Link
        href="/trips"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <Compass className="h-5 w-5 text-primary" />
        <span>Trips</span>
      </Link>
      <Link
        href="/calendar"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <Calendar className="h-5 w-5 text-primary" />
        <span>Calendar</span>
      </Link>

      <div className="pt-4 pb-2">
        <div className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          Quick Actions
        </div>
      </div>

      <Link
        href="/squads/create"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <PlusCircle className="h-5 w-5 text-primary" />
        <span>Create Squad</span>
      </Link>
      <Link
        href="/trips/create"
        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        onClick={onClick}
      >
        <PlusCircle className="h-5 w-5 text-primary" />
        <span>Plan a Trip</span>
      </Link>

      <div className="pt-4 mt-4 border-t">
        <Link
          href="/settings"
          className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
          onClick={onClick}
        >
          <Settings className="h-5 w-5 text-muted-foreground" />
          <span>Settings</span>
        </Link>
        <a
          href="https://vdc.formaloo.me/brotrips-ai-feedback"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted"
        >
          <MessageSquare className="h-5 w-5 text-muted-foreground" />
          <span>Feedback</span>
        </a>
        <button
          onClick={handleLogout}
          className="flex w-full items-center gap-3 px-3 py-2 rounded-md hover:bg-muted text-left"
        >
          <LogOut className="h-5 w-5 text-muted-foreground" />
          <span>Logout</span>
        </button>
      </div>
    </nav>
  )
}

// Mobile sidebar component
function MobileSidebar() {
  const { open, setOpen, isMobile } = useSidebar()

  // Close the sidebar when navigating
  const handleNavigation = () => {
    setOpen(false)
  }

  // Only render if we're on mobile
  if (!isMobile) return null

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent side="left" className="w-[280px] sm:w-[320px] p-0 [&>button]:hidden">
        {/* Add SheetTitle and SheetDescription for accessibility - visually hidden */}
        <SheetHeader className="sr-only">
          <SheetTitle>Navigation Menu</SheetTitle>
          <SheetDescription>
            Application navigation menu with links to dashboard, squads, trips, and settings
          </SheetDescription>
        </SheetHeader>

        <div className="flex justify-between items-center p-4 border-b">
          <div className="flex items-center gap-2">
            <Compass className="h-5 w-5 text-primary" />
            <span className="font-semibold">BroTrips.ai</span>
          </div>
          <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
            <X className="h-5 w-5" />
          </Button>
        </div>
        <div className="p-4">
          <NavigationLinks onClick={handleNavigation} />
        </div>
      </SheetContent>
    </Sheet>
  )
}

// Desktop sidebar component
function DesktopSidebar() {
  const { isMobile } = useSidebar()

  // Only render if we're not on mobile
  if (isMobile) return null

  return (
    <aside className="hidden md:block w-64 border-r p-4">
      <NavigationLinks />
    </aside>
  )
}

// Main AppSidebar component that includes both mobile and desktop versions
export function AppSidebar() {
  return (
    <>
      <MobileSidebar />
      <DesktopSidebar />
    </>
  )
}
